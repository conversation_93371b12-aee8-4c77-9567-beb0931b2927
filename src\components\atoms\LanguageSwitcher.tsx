import React from 'react';
import { useTranslation } from 'react-i18next';
import { Globe } from 'lucide-react';
import Button from './Button';

const LanguageSwitcher: React.FC = () => {
  const { i18n, t } = useTranslation();

  const toggleLanguage = () => {
    const newLanguage = i18n.language === 'en' ? 'fr' : 'en';
    i18n.changeLanguage(newLanguage);
  };

  const currentLanguage = i18n.language === 'en' ? 'english' : 'french';
  const targetLanguage = i18n.language === 'en' ? 'french' : 'english';

  return (
    <Button
      variant="outline-quantum"
      size="sm"
      onClick={toggleLanguage}
      className="group relative overflow-hidden border-consciousness/30 hover:border-consciousness/60 transition-all duration-300"
      leftIcon={<Globe className="w-4 h-4" />}
    >
      <span className="relative z-10 text-sm font-medium">
        {t(`language.${targetLanguage}`)}
      </span>
      <div className="absolute inset-0 bg-gradient-to-r from-consciousness/10 to-harmony/10 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
    </Button>
  );
};

export default LanguageSwitcher;
