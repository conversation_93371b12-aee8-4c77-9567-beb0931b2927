
import React from 'react';
import {
  Brain,
  Zap,
  Network,
  Cpu
} from 'lucide-react';
import Typography from '../atoms/Typography';
import Card from '../atoms/Card';
import Button from '../atoms/Button';

const ACITechnology: React.FC = () => {

  const characteristics = [
    {
      icon: Network,
      title: "Decentralized Intelligence",
      description: "Like cells in an organism, ACI agents operate with local autonomy while contributing to emergent system-wide intelligence.",
      color: "consciousness"
    },
    {
      icon: Zap,
      title: "Self-Optimization", 
      description: "Continuously adapt parameters and connections based on live data streams, learning without human intervention.",
      color: "creativity"
    },
    {
      icon: Brain,
      title: "Emergent Behavior",
      description: "Complex strategies arise from simple interaction rules, solving problems in ways not explicitly programmed.",
      color: "intuition"
    },
    {
      icon: Cpu,
      title: "Radical Scalability",
      description: "Lightweight, decentralized nature scales from IoT sensors to global supply chains with unparalleled efficiency.",
      color: "harmony"
    }
  ];



  return (
    <section id="aci" className="py-32 relative">
      {/* Quantum Background */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-1/3 left-1/5 w-40 h-40 bg-consciousness/8 rounded-cellular blur-2xl animate-consciousness-wave"></div>
        <div className="absolute bottom-1/3 right-1/5 w-32 h-32 bg-creativity/10 rounded-biomorphic blur-xl animate-neural-pulse"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-20">
          <Typography 
            as="h2" 
            variant="4xl" 
            weight="bold" 
            gradient="holographic"
            align="center"
            className="mb-6"
          >
            Artificial Cellular Intelligence
          </Typography>
          
          <Typography 
            as="p" 
            variant="xl" 
            color="secondary" 
            align="center"
            className="mb-6"
          >
            Beyond Conventional AI: A New Paradigm
          </Typography>

          <Typography 
            as="p" 
            variant="lg" 
            color="tertiary" 
            align="center"
            className="max-w-4xl mx-auto leading-relaxed"
          >
            To power the symbiotic paradigm, a new form of intelligence is required. SymbioWave's 
            foundational asset is its proprietary Artificial Cellular Intelligence (ACI).
          </Typography>
        </div>

        {/* ACI vs Traditional AI Comparison */}
        <Card variant="quantum" className="mb-20 overflow-hidden border-consciousness/20">
          <div className="grid lg:grid-cols-2 gap-8">
            <div className="p-8">
              <Typography variant="xl" weight="bold" color="creativity" className="mb-6">
                Traditional AI
              </Typography>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-creativity/60 rounded-full mt-2 flex-shrink-0"></div>
                  <Typography variant="sm" color="tertiary">Centralized, monolithic, pre-trained models</Typography>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-creativity/60 rounded-full mt-2 flex-shrink-0"></div>
                  <Typography variant="sm" color="tertiary">Static learning requiring costly retraining</Typography>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-creativity/60 rounded-full mt-2 flex-shrink-0"></div>
                  <Typography variant="sm" color="tertiary">Brittle, vulnerable to novel data</Typography>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-creativity/60 rounded-full mt-2 flex-shrink-0"></div>
                  <Typography variant="sm" color="tertiary">Extremely high computational cost</Typography>
                </div>
              </div>
            </div>

            <div className="p-8 bg-consciousness/5 rounded-r-lg">
              <Typography variant="xl" weight="bold" color="consciousness" className="mb-6">
                Artificial Cellular Intelligence
              </Typography>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-consciousness/80 rounded-full mt-2 flex-shrink-0"></div>
                  <Typography variant="sm" color="secondary">Decentralized ecosystem of autonomous cells</Typography>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-consciousness/80 rounded-full mt-2 flex-shrink-0"></div>
                  <Typography variant="sm" color="secondary">Dynamic evolution, continuous real-time adaptation</Typography>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-consciousness/80 rounded-full mt-2 flex-shrink-0"></div>
                  <Typography variant="sm" color="secondary">Anti-fragile, self-healing and robust</Typography>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-consciousness/80 rounded-full mt-2 flex-shrink-0"></div>
                  <Typography variant="sm" color="secondary">Lightweight, edge-to-cloud scalability</Typography>
                </div>
              </div>
            </div>
          </div>
        </Card>

        {/* Key Characteristics */}
        <div className="mb-20">
          <Typography 
            as="h3" 
            variant="2xl" 
            weight="bold" 
            color="consciousness"
            align="center"
            className="mb-12"
          >
            Key Characteristics of ACI
          </Typography>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {characteristics.map((char, index) => {
              const IconComponent = char.icon;
              return (
                <Card 
                  key={char.title}
                  variant="neural"
                  className={`group text-center hover:border-${char.color}/50 transition-all duration-bio hover:transform hover:scale-105`}
                >
                  <div className={`w-16 h-16 mx-auto mb-4 rounded-cellular bg-${char.color}/10 flex items-center justify-center group-hover:animate-cellular-flow`}>
                    <IconComponent className={`w-8 h-8 text-${char.color}`} />
                  </div>
                  <Typography variant="lg" weight="semibold" className={`mb-3 text-${char.color}`}>
                    {char.title}
                  </Typography>
                  <Typography variant="xs" color="secondary" className="leading-relaxed">
                    {char.description}
                  </Typography>
                </Card>
              );
            })}
          </div>
        </div>

        {/* Call to Action for Core Models */}
        <div className="text-center">
          <Card variant="quantum" className="p-12 border-consciousness/30 inline-block max-w-4xl">
            <Typography
              as="h3"
              variant="2xl"
              weight="bold"
              gradient="neural"
              className="mb-6"
            >
              The ACI Model Suite: Nature's Algorithms for Business
            </Typography>
            <Typography
              variant="lg"
              color="secondary"
              className="mb-8 leading-relaxed"
            >
              Discover our comprehensive collection of bio-inspired AI models that transform natural intelligence into business solutions.
              Each model is inspired by millions of years of evolutionary optimization.
            </Typography>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Button
                variant="quantum"
                size="xl"
                leftIcon={<Brain className="w-6 h-6" />}
                onClick={() => window.location.href = '/core-models'}
                className="min-w-[200px] font-semibold"
              >
                Explore Core Models
              </Button>
              <Button
                variant="professional"
                size="xl"
                leftIcon={<Zap className="w-6 h-6" />}
                onClick={() => window.location.href = '/symbiocore'}
                className="min-w-[200px] font-semibold"
              >
                Access via SymbioCore
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default ACITechnology;
