
import React from 'react';
import { Globe, Leaf, Zap, TrendingUp } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import Typography from '../atoms/Typography';
import Card from '../atoms/Card';

const GlobalImpact: React.FC = () => {
  const { t } = useTranslation();

  const impactAreas = [
    {
      icon: Globe,
      title: t('globalImpact.impactAreas.autonomousEcosystems.title'),
      description: t('globalImpact.impactAreas.autonomousEcosystems.description'),
      color: "consciousness"
    },
    {
      icon: Leaf,
      title: t('globalImpact.impactAreas.resilientSupplyChains.title'),
      description: t('globalImpact.impactAreas.resilientSupplyChains.description'),
      color: "harmony"
    },
    {
      icon: Zap,
      title: t('globalImpact.impactAreas.symbioticValue.title'),
      description: t('globalImpact.impactAreas.symbioticValue.description'),
      color: "creativity"
    },
    {
      icon: TrendingUp,
      title: t('globalImpact.impactAreas.circularEconomy.title'),
      description: t('globalImpact.impactAreas.circularEconomy.description'),
      color: "intuition"
    }
  ];

  return (
    <section className="py-32 relative">
      {/* Organic Background */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-1/4 left-1/6 w-56 h-56 bg-consciousness/8 rounded-cellular blur-3xl animate-consciousness-wave"></div>
        <div className="absolute bottom-1/4 right-1/6 w-40 h-40 bg-harmony/10 rounded-biomorphic blur-2xl animate-neural-pulse"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-20">
          <Typography
            as="h2"
            variant="4xl"
            weight="bold"
            gradient="holographic"
            align="center"
            className="mb-6"
          >
            {t('globalImpact.title')}
          </Typography>

          <Typography
            as="p"
            variant="xl"
            color="secondary"
            align="center"
            className="mb-6"
          >
            {t('globalImpact.subtitle')}
          </Typography>

          <Typography
            as="p"
            variant="lg"
            color="tertiary"
            align="center"
            className="max-w-4xl mx-auto leading-relaxed"
          >
            {t('globalImpact.description')}
          </Typography>
        </div>

        {/* Impact Areas */}
        <div className="grid md:grid-cols-2 gap-8 mb-20">
          {impactAreas.map((area, index) => {
            const IconComponent = area.icon;
            return (
              <Card 
                key={area.title}
                variant="neural"
                className={`group hover:border-${area.color}/50 transition-all duration-bio hover:transform hover:scale-105`}
              >
                <div className="flex items-start space-x-4">
                  <div className={`w-16 h-16 rounded-cellular bg-${area.color}/10 flex items-center justify-center flex-shrink-0 group-hover:bg-${area.color}/20 transition-colors duration-quantum`}>
                    <IconComponent className={`w-8 h-8 text-${area.color}`} />
                  </div>
                  
                  <div className="flex-1">
                    <Typography 
                      variant="lg" 
                      weight="semibold" 
                      className={`mb-3 text-${area.color}`}
                    >
                      {area.title}
                    </Typography>
                    <Typography 
                      variant="sm" 
                      color="secondary"
                      className="leading-relaxed"
                    >
                      {area.description}
                    </Typography>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>

        {/* Vision Statement */}
        <Card variant="quantum" className="text-center border-consciousness/30">
          <Typography
            as="h3"
            variant="2xl"
            weight="bold"
            gradient="neural"
            className="mb-6"
          >
            {t('globalImpact.vision.title')}
          </Typography>

          <Typography
            as="p"
            variant="lg"
            color="secondary"
            className="mb-8 max-w-4xl mx-auto leading-relaxed"
          >
            {t('globalImpact.vision.description')}
          </Typography>

          <div className="grid md:grid-cols-3 gap-8 text-center">
            <div>
              <Typography variant="2xl" weight="bold" color="consciousness" className="mb-2">
                $500M
              </Typography>
              <Typography variant="sm" color="tertiary">
                {t('globalImpact.metrics.revenue.label')}
              </Typography>
            </div>
            <div>
              <Typography variant="2xl" weight="bold" color="harmony" className="mb-2">
                {t('globalImpact.metrics.gmv.value')}
              </Typography>
              <Typography variant="sm" color="tertiary">
                {t('globalImpact.metrics.gmv.label')}
              </Typography>
            </div>
            <div>
              <Typography variant="2xl" weight="bold" color="creativity" className="mb-2">
                {t('globalImpact.metrics.impact.value')}
              </Typography>
              <Typography variant="sm" color="tertiary">
                {t('globalImpact.metrics.impact.label')}
              </Typography>
            </div>
          </div>
        </Card>
      </div>
    </section>
  );
};

export default GlobalImpact;
