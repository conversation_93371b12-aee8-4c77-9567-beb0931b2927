import React, { useState, useEffect } from 'react';
import { Menu, X, ChevronDown, Brain, Cpu, Network, Globe, Target, Rocket, Factory, TrendingUp, Users, Building2, Microscope, ArrowRightLeft, Shield, DollarSign, Handshake } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import LanguageSwitcher from '../atoms/LanguageSwitcher';

const Button = ({ variant, size, className, onClick, children, ...props }) => {
  const baseClasses = "inline-flex items-center justify-center font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2";
  const variants = {
    quantum: "bg-gradient-to-r from-cyan-500/20 to-purple-500/20 text-white hover:from-cyan-400/30 hover:to-purple-400/30",
    default: "bg-gray-600 text-white hover:bg-gray-700"
  };
  const sizes = {
    sm: "px-3 py-1.5 text-sm",
    md: "px-4 py-2 text-base",
    lg: "px-6 py-3 text-lg"
  };
  
  return (
    <button
      className={`${baseClasses} ${variants[variant] || variants.default} ${sizes[size] || sizes.md} ${className}`}
      onClick={onClick}
      {...props}
    >
      {children}
    </button>
  );
};

const Typography = ({ variant, weight, className, children, ...props }) => {
  const variants = {
    xs: "text-xs",
    sm: "text-sm",
    base: "text-base",
    lg: "text-lg"
  };
  const weights = {
    normal: "font-normal",
    medium: "font-medium",
    semibold: "font-semibold",
    bold: "font-bold"
  };
  
  return (
    <span className={`${variants[variant] || variants.base} ${weights[weight] || weights.normal} ${className}`} {...props}>
      {children}
    </span>
  );
};

const cn = (...classes) => {
  return classes.filter(Boolean).join(' ');
};

const Header = () => {
  const { t } = useTranslation();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      
      if (currentScrollY > lastScrollY && currentScrollY > 50) {
        setIsVisible(false);
      } else if (currentScrollY < lastScrollY) {
        setIsVisible(true);
      }
      
      setIsScrolled(currentScrollY > 10);
      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY]);

  const technologyItems = [
    { label: t('header.technology.whatIsACI.label'), href: '/aci-technology', description: t('header.technology.whatIsACI.description') },
    { label: t('header.technology.coreModels.label'), href: '/core-models', description: t('header.technology.coreModels.description') },
    { label: t('header.technology.technicalFoundation.label'), href: '/technical-foundation', description: t('header.technology.technicalFoundation.description') },
    { label: t('header.technology.innovationEngine.label'), href: '/innovation-engine', description: t('header.technology.innovationEngine.description') },
    { label: t('header.technology.competitiveAdvantage.label'), href: '/competitive-advantage', description: t('header.technology.competitiveAdvantage.description') },
  ];

  const ecosystemItems = [
    { label: t('header.ecosystem.symbioCore.label'), href: '/symbiocore', description: t('header.ecosystem.symbioCore.description') },
    { label: t('header.ecosystem.symbioLabs.label'), href: '/symbiolabs', description: t('header.ecosystem.symbioLabs.description') },
    { label: t('header.ecosystem.symbioAutomate.label'), href: '/symbioautomate', description: t('header.ecosystem.symbioAutomate.description') },
    { label: t('header.ecosystem.symbioXchange.label'), href: '/symbioxchange', description: t('header.ecosystem.symbioXchange.description') },
  ];

  const impactItems = [
    { label: t('header.impact.imperative.label'), href: '/the-imperative', description: t('header.impact.imperative.description') },
    { label: t('header.impact.globalVision.label'), href: '/vision', description: t('header.impact.globalVision.description') },
    { label: t('header.impact.globalImpact.label'), href: '/symbioimpact', description: t('header.impact.globalImpact.description') },
    { label: t('header.impact.aboutUs.label'), href: '/about-us', description: t('header.impact.aboutUs.description') },
  ];

  const divisionsItems = [
    { label: t('header.divisions.symbioCore.label'), href: '/symbiocore', description: t('header.divisions.symbioCore.description'), icon: <Brain className="w-4 h-4" />, status: 'coming-soon' },
    { label: t('header.divisions.symbioLabs.label'), href: '/symbiolabs', description: t('header.divisions.symbioLabs.description'), icon: <Microscope className="w-4 h-4" />, status: 'coming-soon' },
    { label: t('header.divisions.symbioAutomate.label'), href: '/symbioautomate', description: t('header.divisions.symbioAutomate.description'), icon: <Factory className="w-4 h-4" />, status: 'active' },
    { label: t('header.divisions.symbioXchange.label'), href: '/symbioxchange', description: t('header.divisions.symbioXchange.description'), icon: <ArrowRightLeft className="w-4 h-4" />, status: 'coming-soon' },
    { label: t('header.divisions.symbioEdge.label'), href: '/symbioedge', description: t('header.divisions.symbioEdge.description'), icon: <Network className="w-4 h-4" />, status: 'coming-soon' },
    { label: t('header.divisions.symbioImpact.label'), href: '/symbioimpact', description: t('header.divisions.symbioImpact.description'), icon: <Shield className="w-4 h-4" />, status: 'coming-soon' },
    { label: t('header.divisions.symbioVentures.label'), href: '/symbioventures', description: t('header.divisions.symbioVentures.description'), icon: <DollarSign className="w-4 h-4" />, status: 'coming-soon' },
    { label: t('header.divisions.symbioAlliance.label'), href: '/symbioalliance', description: t('header.divisions.symbioAlliance.description'), icon: <Handshake className="w-4 h-4" />, status: 'coming-soon' },
  ];

  const handleLogoClick = () => {
    window.location.href = '/';
  };

  const handleJoinUsClick = () => {
    window.location.href = '/interface';
  };

  const DropdownMenu = ({ items, title }) => (
    <div className="absolute top-full left-0 mt-4 w-[24rem] opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform group-hover:translate-y-0 translate-y-4 z-[100000]">
      <div className="relative bg-gradient-to-br from-gray-900/95 via-slate-800/98 to-gray-900/95 backdrop-blur-3xl rounded-3xl border border-cyan-300/20 shadow-2xl overflow-hidden">
        <div className="relative p-6 z-10">
          <div className="space-y-2">
            {items.map((item, index) => (
              <a
                key={item.label}
                href={item.href}
                className="group/item flex items-start space-x-4 p-4 rounded-2xl transition-all duration-300 relative overflow-hidden bg-gradient-to-r from-white/2 to-white/1 hover:from-cyan-400/8 hover:via-purple-500/5 hover:to-cyan-400/8 border border-white/5 hover:border-cyan-400/25"
              >
                <div className="relative w-10 h-10 rounded-full bg-gradient-to-br from-cyan-400/10 via-purple-500/5 to-cyan-400/10 flex items-center justify-center group-hover/item:from-cyan-400/20 group-hover/item:via-purple-500/15 group-hover/item:to-cyan-400/20 transition-all duration-300 border border-cyan-400/15 group-hover/item:border-cyan-400/30 backdrop-blur-sm">
                  <div className="relative z-10">
                    {item.icon || <div className="w-3 h-3 rounded-full bg-gradient-to-r from-cyan-400 to-purple-400 shadow-sm shadow-cyan-400/20" />}
                  </div>
                </div>
                
                <div className="flex-1 relative z-10">
                  <div className="flex items-center gap-3 mb-1">
                    <Typography 
                      variant="sm" 
                      weight="semibold" 
                      className="group-hover/item:bg-gradient-to-r group-hover/item:from-cyan-300 group-hover/item:via-white group-hover/item:to-purple-300 group-hover/item:bg-clip-text group-hover/item:text-transparent transition-all duration-300 text-white/90 font-mono tracking-wide"
                    >
                      {item.label}
                    </Typography>
                    {item.status === 'active' && (
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-cyan-400 shadow-lg shadow-cyan-400/50" />
                        <span className="px-3 py-1 text-xs bg-gradient-to-r from-cyan-400/20 to-cyan-400/30 text-cyan-300 rounded-full font-bold border border-cyan-400/30 backdrop-blur-sm font-mono">
                          ACTIVE
                        </span>
                      </div>
                    )}
                    {item.status === 'coming-soon' && (
                      <span className="px-3 py-1 text-xs bg-gradient-to-r from-purple-400/20 to-purple-400/30 text-purple-300 rounded-full font-bold border border-purple-400/30 backdrop-blur-sm font-mono">
                        INITIALIZING
                      </span>
                    )}
                  </div>
                  <Typography 
                    variant="xs" 
                    weight="normal"
                    className="leading-snug opacity-60 group-hover/item:opacity-85 transition-opacity duration-300 text-white/70 font-light tracking-wide"
                  >
                    {item.description}
                  </Typography>
                </div>
              </a>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <header className={cn(
      "fixed top-0 left-0 right-0 w-full z-[100000] transition-all duration-300 ease-out",
      "bg-gradient-to-r from-cyan-300/20 via-gray-800/90 to-black/95",
      "backdrop-blur-3xl border-b border-cyan-300/10",
      "shadow-2xl shadow-black/50",
      isScrolled ? "py-2" : "py-4",
      isVisible ? "translate-y-0" : "-translate-y-full"
    )}>
      <div className="w-full px-6 lg:px-8">
        {/* Force full width layout with CSS Grid - No container constraints */}
        <div className="w-full grid grid-cols-[1fr_auto_1fr] items-center gap-8 h-16">
          
          {/* Logo Section - Left Side - Flex Start */}
          <div className="flex justify-start items-center h-full">
            <div 
              className="flex items-center cursor-pointer group transition-all duration-300 ease-out hover:scale-[1.02]"
              onClick={handleLogoClick}
            >
              <div className="relative flex items-center justify-center w-64 h-16 transition-all duration-300 ease-out">
                <img 
                  src="/lovable-uploads/craiyon_160948_image.png" 
                  alt="SymbioWave" 
                  className="w-full h-full object-contain relative z-10 transition-all duration-300 ease-out group-hover:brightness-125"
                />
              </div>
            </div>
          </div>

          {/* Navigation - Center - Force center with flex */}
          <div className="flex items-center justify-center h-full">
            <div className={cn(
              "hidden lg:flex items-center justify-center space-x-6 transition-all duration-300 h-full"
            )}>
              
              {/* Divisions */}
              <div className="relative group z-[99999] flex items-center h-full">
                <button className={cn(
                  "flex items-center space-x-2 text-white/80 hover:text-white transition-all duration-300 ease-out rounded-2xl hover:bg-gradient-to-r hover:from-cyan-400/10 hover:via-purple-500/5 hover:to-cyan-400/10 border border-transparent hover:border-cyan-400/20 relative z-10 backdrop-blur-sm group/btn",
                  "px-4 py-2 text-sm"
                )}>
                  <div className="relative w-6 h-6 rounded-full bg-gradient-to-br from-cyan-400/20 to-purple-500/30 flex items-center justify-center transition-all duration-300 group-hover/btn:from-cyan-400/40 group-hover/btn:to-purple-500/50 border border-cyan-400/20 group-hover/btn:border-cyan-400/40 backdrop-blur-sm">
                    <Building2 className="w-3 h-3 text-cyan-300" />
                  </div>
                  <Typography variant="sm" weight="semibold" className="tracking-wide font-mono text-white/90 whitespace-nowrap">
                    {t('header.divisions.title')}
                  </Typography>
                  <ChevronDown className="w-3 h-3 transition-all duration-300 group-hover:rotate-180 text-white/60 group-hover:text-cyan-300" />
                </button>
                <DropdownMenu items={divisionsItems} title="" />
              </div>

              {/* ACI Technology */}
              <div className="relative group z-[99998] flex items-center h-full">
                <button className={cn(
                  "flex items-center space-x-2 text-white/80 hover:text-white transition-all duration-300 ease-out rounded-2xl hover:bg-gradient-to-r hover:from-cyan-400/10 hover:via-purple-500/5 hover:to-cyan-400/10 border border-transparent hover:border-cyan-400/20 relative z-10 backdrop-blur-sm group/btn",
                  "px-4 py-2 text-sm"
                )}>
                  <div className="relative w-6 h-6 rounded-full bg-gradient-to-br from-purple-400/20 to-cyan-500/30 flex items-center justify-center transition-all duration-300 group-hover/btn:from-purple-400/40 group-hover/btn:to-cyan-500/50 border border-purple-400/20 group-hover/btn:border-purple-400/40 backdrop-blur-sm">
                    <Cpu className="w-3 h-3 text-purple-300" />
                  </div>
                  <Typography variant="sm" weight="semibold" className="tracking-wide font-mono text-white/90 whitespace-nowrap">
                    {t('header.technology.title')}
                  </Typography>
                  <ChevronDown className="w-3 h-3 transition-all duration-300 group-hover:rotate-180 text-white/60 group-hover:text-purple-300" />
                </button>
                <DropdownMenu items={technologyItems} title="" />
              </div>

              {/* Ecosystem */}
              <div className="relative group z-[99997] flex items-center h-full">
                <button className={cn(
                  "flex items-center space-x-2 text-white/80 hover:text-white transition-all duration-300 ease-out rounded-2xl hover:bg-gradient-to-r hover:from-cyan-400/10 hover:via-purple-500/5 hover:to-cyan-400/10 border border-transparent hover:border-cyan-400/20 relative z-10 backdrop-blur-sm group/btn",
                  "px-4 py-2 text-sm"
                )}>
                  <div className="relative w-6 h-6 rounded-full bg-gradient-to-br from-emerald-400/20 to-cyan-500/30 flex items-center justify-center transition-all duration-300 group-hover/btn:from-emerald-400/40 group-hover/btn:to-cyan-500/50 border border-emerald-400/20 group-hover/btn:border-emerald-400/40 backdrop-blur-sm">
                    <Globe className="w-3 h-3 text-emerald-300" />
                  </div>
                  <Typography variant="sm" weight="semibold" className="tracking-wide font-mono text-white/90 whitespace-nowrap">
                    {t('header.ecosystem.title')}
                  </Typography>
                  <ChevronDown className="w-3 h-3 transition-all duration-300 group-hover:rotate-180 text-white/60 group-hover:text-emerald-300" />
                </button>
                <DropdownMenu items={ecosystemItems} title="" />
              </div>

              {/* Impact */}
              <div className="relative group z-[99996] flex items-center h-full">
                <button className={cn(
                  "flex items-center space-x-2 text-white/80 hover:text-white transition-all duration-300 ease-out rounded-2xl hover:bg-gradient-to-r hover:from-cyan-400/10 hover:via-purple-500/5 hover:to-cyan-400/10 border border-transparent hover:border-cyan-400/20 relative z-10 backdrop-blur-sm group/btn",
                  "px-4 py-2 text-sm"
                )}>
                  <div className="relative w-6 h-6 rounded-full bg-gradient-to-br from-rose-400/20 to-orange-500/30 flex items-center justify-center transition-all duration-300 group-hover/btn:from-rose-400/40 group-hover/btn:to-orange-500/50 border border-rose-400/20 group-hover/btn:border-rose-400/40 backdrop-blur-sm">
                    <Target className="w-3 h-3 text-rose-300" />
                  </div>
                  <Typography variant="sm" weight="semibold" className="tracking-wide font-mono text-white/90 whitespace-nowrap">
                    {t('header.impact.title')}
                  </Typography>
                  <ChevronDown className="w-3 h-3 transition-all duration-300 group-hover:rotate-180 text-white/60 group-hover:text-rose-300" />
                </button>
                <DropdownMenu items={impactItems} title="" />
              </div>
            </div>
          </div>

          {/* Join Us Button - Right Side - Flex End */}
          <div className="flex justify-end items-center h-full space-x-4">
            {/* Language Switcher */}
            <div className="hidden lg:block">
              <LanguageSwitcher />
            </div>

            {/* Desktop Join Us Button - Subconscious Psychology Design */}
            <div className="hidden lg:block">
              <Button
                variant="quantum"
                size="md"
                className={cn(
                  "relative overflow-hidden group transition-all duration-700 ease-[cubic-bezier(0.23,1,0.32,1)]",
                  "hover:scale-[1.12] hover:-translate-y-1 hover:rotate-[0.5deg]",
                  // Psychological golden ratio proportions and fibonacci spacing
                  "px-10 py-4 text-sm font-black tracking-[0.05em]",
                  // Neuromorphic design with depth perception
                  "rounded-[21px]", // Golden ratio derived radius
                  // Multi-layered border system for depth psychology
                  "border-[3px] border-transparent",
                  "bg-gradient-to-r from-emerald-400/40 via-green-300/50 to-emerald-400/40 bg-clip-border",
                  "hover:from-emerald-300/60 hover:via-green-200/70 hover:to-emerald-300/60",
                  // Subconscious color psychology - trust, growth, prosperity
                  "bg-gradient-to-br from-slate-900/98 via-emerald-950/95 to-slate-900/98",
                  "hover:from-emerald-950/98 hover:via-green-900/95 hover:to-emerald-950/98",
                  // Advanced shadow system mimicking natural light
                  "shadow-[0_8px_32px_rgba(16,185,129,0.12),0_2px_8px_rgba(16,185,129,0.08)]",
                  "hover:shadow-[0_16px_64px_rgba(16,185,129,0.25),0_8px_32px_rgba(16,185,129,0.15),0_0_0_1px_rgba(16,185,129,0.1)]",
                  "backdrop-blur-[20px] backdrop-saturate-[1.8]",
                  // Fibonacci sequence based animations for natural rhythm
                  "before:absolute before:inset-0 before:rounded-[21px]",
                  "before:bg-gradient-conic before:from-emerald-400/0 before:via-emerald-400/30 before:to-emerald-400/0",
                  "before:translate-x-[-233%] hover:before:translate-x-[233%]", // 233 = fibonacci number
                  "before:transition-transform before:duration-[1618ms] before:ease-[cubic-bezier(0.618,0,0.382,1)]", // 1618ms = golden ratio
                  // Secondary shimmer effect for attention capture
                  "after:absolute after:inset-0 after:rounded-[21px] after:opacity-0 hover:after:opacity-100",
                  "after:bg-gradient-to-r after:from-transparent after:via-white/10 after:to-transparent",
                  "after:translate-x-[-100%] hover:after:translate-x-[100%]",
                  "after:transition-all after:duration-[987ms] after:delay-[377ms]" // Fibonacci timing
                )}
                onClick={handleJoinUsClick}
              >
                {/* Neuromorphic depth layers for 3D perception */}
                <div className="absolute inset-0 rounded-[21px] bg-gradient-to-br from-emerald-400/0 via-emerald-400/20 to-emerald-400/0 opacity-0 group-hover:opacity-100 transition-opacity duration-[610ms] blur-[2px]"></div>
                <div className="absolute inset-[2px] rounded-[19px] bg-gradient-to-tl from-emerald-500/0 via-emerald-500/15 to-emerald-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-[377ms] delay-[144ms]"></div>

                {/* Fibonacci spiral particle system for natural movement */}
                <div className="absolute inset-0 overflow-hidden rounded-[21px]">
                  {/* Golden ratio positioned elements */}
                  <div className="absolute top-[38.2%] left-[61.8%] w-[3px] h-[3px] bg-emerald-300 rounded-full opacity-70 group-hover:animate-ping group-hover:animate-duration-[1597ms]"></div>
                  <div className="absolute bottom-[61.8%] right-[38.2%] w-[2px] h-[2px] bg-green-200 rounded-full opacity-50 group-hover:animate-pulse group-hover:animate-duration-[987ms]"></div>
                  <div className="absolute top-[61.8%] right-[23.6%] w-[1px] h-[1px] bg-emerald-400 rounded-full opacity-60 group-hover:animate-bounce group-hover:animate-duration-[610ms]"></div>
                  <div className="absolute bottom-[23.6%] left-[76.4%] w-[1.5px] h-[1.5px] bg-emerald-200 rounded-full opacity-40 group-hover:animate-ping group-hover:animate-duration-[377ms]"></div>

                  {/* Subliminal success symbols */}
                  <div className="absolute top-[14.6%] left-[85.4%] w-[1px] h-[1px] bg-emerald-100 rounded-full opacity-30 group-hover:opacity-80 transition-opacity duration-[233ms]"></div>
                  <div className="absolute bottom-[14.6%] right-[85.4%] w-[1px] h-[1px] bg-green-100 rounded-full opacity-30 group-hover:opacity-80 transition-opacity duration-[144ms]"></div>
                </div>

                {/* Biometric rhythm pulse for trust building */}
                <div className="absolute inset-0 rounded-[21px] opacity-0 group-hover:opacity-100 transition-opacity duration-[987ms]">
                  <div className="absolute inset-[1px] rounded-[20px] bg-gradient-to-r from-transparent via-emerald-400/5 to-transparent animate-pulse" style={{animationDuration: '4.236s'}}></div>
                </div>

                <span className="relative z-30 flex items-center justify-center space-x-4">
                  {/* Psychological status indicator - mimics heartbeat for trust */}
                  <div className="relative flex items-center justify-center">
                    {/* Outer pulse ring - subconscious attention grabber */}
                    <div className="absolute w-6 h-6 bg-emerald-400/20 rounded-full animate-ping opacity-0 group-hover:opacity-100 transition-opacity duration-[610ms]" style={{animationDuration: '3.618s'}}></div>
                    <div className="absolute w-4 h-4 bg-emerald-300/30 rounded-full animate-ping opacity-0 group-hover:opacity-100 transition-opacity duration-[377ms] delay-[233ms]" style={{animationDuration: '2.618s'}}></div>

                    {/* Core indicator with golden ratio sizing */}
                    <div className="relative w-[13px] h-[13px] bg-gradient-to-br from-emerald-300 via-emerald-400 to-emerald-500 rounded-full shadow-[0_0_20px_rgba(16,185,129,0.6)] group-hover:shadow-[0_0_30px_rgba(16,185,129,0.8)] transition-all duration-[377ms]">
                      {/* Inner light core - represents exclusivity */}
                      <div className="absolute inset-[2px] bg-gradient-to-br from-white/90 via-emerald-100/80 to-white/70 rounded-full group-hover:from-white group-hover:via-white/90 group-hover:to-emerald-50 transition-all duration-[233ms]"></div>
                      {/* Micro pulse for life-like quality */}
                      <div className="absolute inset-[4px] bg-emerald-200 rounded-full opacity-80 group-hover:opacity-100 animate-pulse transition-opacity duration-[144ms]" style={{animationDuration: '1.618s'}}></div>
                    </div>

                    {/* Subliminal success indicators */}
                    <div className="absolute -top-1 -right-1 w-[3px] h-[3px] bg-emerald-200 rounded-full opacity-0 group-hover:opacity-70 transition-opacity duration-[987ms] delay-[377ms]"></div>
                    <div className="absolute -bottom-1 -left-1 w-[2px] h-[2px] bg-green-300 rounded-full opacity-0 group-hover:opacity-60 transition-opacity duration-[610ms] delay-[233ms]"></div>
                  </div>

                  {/* Psychologically optimized typography */}
                  <div className="relative">
                    {/* Subliminal glow for importance */}
                    <div className="absolute inset-0 bg-gradient-to-r from-emerald-100 via-white to-emerald-100 bg-clip-text text-transparent blur-[1px] opacity-0 group-hover:opacity-30 transition-opacity duration-[377ms]">
                      {t('header.joinUs')}
                    </div>

                    {/* Main text with golden ratio letter spacing */}
                    <span className={cn(
                      "relative font-black text-[16px] whitespace-nowrap transition-all duration-[610ms] ease-[cubic-bezier(0.618,0,0.382,1)]",
                      "bg-gradient-to-r from-emerald-50 via-white to-emerald-50",
                      "group-hover:from-white group-hover:via-emerald-25 group-hover:to-white",
                      "bg-clip-text text-transparent",
                      "drop-shadow-[0_1px_3px_rgba(16,185,129,0.3)] group-hover:drop-shadow-[0_2px_8px_rgba(16,185,129,0.4)]",
                      "tracking-[0.618px] group-hover:tracking-[1px]", // Golden ratio spacing
                      "transform group-hover:scale-[1.05] transition-transform duration-[377ms]"
                    )}>
                      {t('header.joinUs')}
                    </span>

                    {/* Subliminal success word overlay */}
                    <div className="absolute inset-0 bg-gradient-to-r from-emerald-200/0 via-emerald-100/20 to-emerald-200/0 bg-clip-text text-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-[1597ms] delay-[233ms]">
                      {t('header.joinUs')}
                    </div>
                  </div>

                  {/* Fibonacci spiral motion indicator */}
                  <div className="relative overflow-hidden w-6 h-4">
                    <div className="absolute inset-0 flex items-center transition-all duration-[377ms] group-hover:translate-x-[2px] group-hover:scale-110">
                      {/* Golden ratio positioned elements */}
                      <div className="w-[5px] h-[5px] bg-gradient-to-br from-emerald-300 to-emerald-400 rounded-full opacity-90 group-hover:opacity-100 transition-all duration-[233ms] shadow-[0_0_8px_rgba(16,185,129,0.4)]"></div>
                      <div className="w-[3px] h-[3px] bg-gradient-to-br from-emerald-200 to-emerald-300 rounded-full ml-[3px] opacity-70 group-hover:opacity-95 transition-all duration-[233ms] delay-[89ms] shadow-[0_0_6px_rgba(16,185,129,0.3)]"></div>
                      <div className="w-[2px] h-[2px] bg-gradient-to-br from-emerald-100 to-emerald-200 rounded-full ml-[2px] opacity-50 group-hover:opacity-80 transition-all duration-[233ms] delay-[144ms] shadow-[0_0_4px_rgba(16,185,129,0.2)]"></div>
                      <div className="w-[1px] h-[1px] bg-emerald-100 rounded-full ml-[1px] opacity-30 group-hover:opacity-60 transition-all duration-[233ms] delay-[233ms]"></div>
                    </div>

                    {/* Motion trail effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-emerald-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-[610ms] blur-[1px]"></div>
                  </div>
                </span>

                {/* Neuromorphic holographic field */}
                <div className="absolute -inset-[3px] rounded-[24px] bg-gradient-conic from-emerald-400/0 via-green-300/20 to-emerald-400/0 opacity-0 group-hover:opacity-100 transition-opacity duration-[987ms] blur-[3px] animate-spin" style={{animationDuration: '20s'}}></div>

                {/* Quantum field effect for premium feel */}
                <div className="absolute -inset-[1px] rounded-[22px] bg-gradient-to-r from-transparent via-emerald-400/8 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-[1597ms] blur-[8px]"></div>

                {/* Subliminal depth perception enhancer */}
                <div className="absolute inset-[1px] rounded-[20px] bg-gradient-to-br from-emerald-500/5 via-transparent to-green-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-[610ms] delay-[377ms]"></div>
              </Button>
            </div>

            {/* Mobile Menu Button */}
            <button
              className={cn(
                "lg:hidden rounded-2xl bg-gradient-to-br from-black/90 to-gray-900/95 backdrop-blur-2xl border border-cyan-400/20 transition-all duration-300 ease-out hover:border-cyan-400/40 hover:bg-gradient-to-br hover:from-cyan-400/10 hover:to-purple-500/10 relative group",
                "p-3"
              )}
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? (
                <X className="w-5 h-5 text-cyan-300 relative z-10" />
              ) : (
                <Menu className="w-5 h-5 text-cyan-300 relative z-10" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div
          className={cn(
            'lg:hidden mt-4 space-y-4 transition-all duration-300 ease-out overflow-hidden rounded-3xl border border-cyan-400/20 backdrop-blur-3xl relative z-[100000]',
            isMenuOpen ? 'max-h-screen opacity-100 p-6' : 'max-h-0 opacity-0 p-0'
          )}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-gray-900/95 via-slate-800/98 to-gray-900/95 rounded-3xl" />
          
          <div className="relative space-y-4 z-10">
            {divisionsItems.map((item) => (
              <a
                key={item.label}
                href={item.href}
                className="flex items-center space-x-4 py-4 px-5 text-white/80 hover:text-white transition-all duration-300 ease-out rounded-2xl hover:bg-gradient-to-r hover:from-cyan-400/10 hover:via-purple-500/5 hover:to-cyan-400/10 border border-transparent hover:border-cyan-400/25 group bg-gradient-to-r from-white/2 to-white/1 relative overflow-hidden"
                onClick={() => setIsMenuOpen(false)}
              >
                <div className="relative w-8 h-8 rounded-full bg-gradient-to-br from-cyan-400/15 via-purple-500/10 to-cyan-400/15 flex items-center justify-center border border-cyan-400/20 group-hover:scale-105 group-hover:border-cyan-400/40 transition-all duration-300 backdrop-blur-sm">
                  {item.icon}
                </div>
                
                <div className="flex-1">
                  <Typography variant="sm" weight="semibold" className="text-sm tracking-wide text-white font-mono group-hover:text-cyan-200 transition-colors duration-300">
                    {item.label}
                  </Typography>
                </div>
                
                {item.status === 'active' && (
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 rounded-full bg-cyan-400 shadow-sm shadow-cyan-400/50" />
                    <span className="px-3 py-1 text-xs bg-gradient-to-r from-cyan-400/20 to-cyan-400/30 text-cyan-300 rounded-full font-bold border border-cyan-400/30 backdrop-blur-sm font-mono">
                      {t('common.status.active').toUpperCase()}
                    </span>
                  </div>
                )}
                {item.status === 'coming-soon' && (
                  <span className="px-3 py-1 text-xs bg-gradient-to-r from-purple-400/20 to-purple-400/30 text-purple-300 rounded-full font-bold border border-purple-400/30 backdrop-blur-sm font-mono">
                    {t('common.status.comingSoon').toUpperCase()}
                  </span>
                )}
              </a>
            ))}
            
            {/* Mobile Language Switcher */}
            <div className="pt-4 border-t border-emerald-400/20 relative z-10">
              <LanguageSwitcher />
            </div>

            {/* Mobile Join Us Button - Subconscious Psychology Design */}
            <div className="pt-4 border-t border-emerald-400/20 relative z-10">
              <Button
                variant="quantum"
                size="lg"
                className={cn(
                  "w-full relative group overflow-hidden transition-all duration-700 ease-[cubic-bezier(0.23,1,0.32,1)]",
                  "hover:scale-[1.06] hover:-translate-y-1",
                  // Psychological golden ratio proportions
                  "py-5 text-sm font-black tracking-[0.05em]",
                  // Neuromorphic design with depth perception
                  "rounded-[21px]", // Golden ratio derived radius
                  // Multi-layered border system for depth psychology
                  "border-[3px] border-transparent",
                  "bg-gradient-to-r from-emerald-400/40 via-green-300/50 to-emerald-400/40 bg-clip-border",
                  "hover:from-emerald-300/60 hover:via-green-200/70 hover:to-emerald-300/60",
                  // Subconscious color psychology
                  "bg-gradient-to-br from-slate-900/98 via-emerald-950/95 to-slate-900/98",
                  "hover:from-emerald-950/98 hover:via-green-900/95 hover:to-emerald-950/98",
                  // Advanced shadow system
                  "shadow-[0_8px_32px_rgba(16,185,129,0.12),0_2px_8px_rgba(16,185,129,0.08)]",
                  "hover:shadow-[0_16px_64px_rgba(16,185,129,0.25),0_8px_32px_rgba(16,185,129,0.15)]",
                  "backdrop-blur-[20px] backdrop-saturate-[1.8]",
                  // Fibonacci sequence based animations
                  "before:absolute before:inset-0 before:rounded-[21px]",
                  "before:bg-gradient-conic before:from-emerald-400/0 before:via-emerald-400/30 before:to-emerald-400/0",
                  "before:translate-x-[-233%] hover:before:translate-x-[233%]",
                  "before:transition-transform before:duration-[1618ms] before:ease-[cubic-bezier(0.618,0,0.382,1)]",
                  // Secondary shimmer effect
                  "after:absolute after:inset-0 after:rounded-[21px] after:opacity-0 hover:after:opacity-100",
                  "after:bg-gradient-to-r after:from-transparent after:via-white/10 after:to-transparent",
                  "after:translate-x-[-100%] hover:after:translate-x-[100%]",
                  "after:transition-all after:duration-[987ms] after:delay-[377ms]"
                )}
                onClick={() => {
                  setIsMenuOpen(false);
                  handleJoinUsClick();
                }}
              >
                {/* Mobile - Same neuromorphic effects as desktop */}
                <div className="absolute inset-0 rounded-[21px] bg-gradient-to-br from-emerald-400/0 via-emerald-400/20 to-emerald-400/0 opacity-0 group-hover:opacity-100 transition-opacity duration-[610ms] blur-[2px]"></div>
                <div className="absolute inset-[2px] rounded-[19px] bg-gradient-to-tl from-emerald-500/0 via-emerald-500/15 to-emerald-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-[377ms] delay-[144ms]"></div>

                {/* Mobile fibonacci particle system */}
                <div className="absolute inset-0 overflow-hidden rounded-[21px]">
                  <div className="absolute top-[38.2%] left-[61.8%] w-[3px] h-[3px] bg-emerald-300 rounded-full opacity-70 group-hover:animate-ping group-hover:animate-duration-[1597ms]"></div>
                  <div className="absolute bottom-[61.8%] right-[38.2%] w-[2px] h-[2px] bg-green-200 rounded-full opacity-50 group-hover:animate-pulse group-hover:animate-duration-[987ms]"></div>
                  <div className="absolute top-[61.8%] right-[23.6%] w-[1px] h-[1px] bg-emerald-400 rounded-full opacity-60 group-hover:animate-bounce group-hover:animate-duration-[610ms]"></div>
                </div>

                {/* Mobile biometric rhythm pulse */}
                <div className="absolute inset-0 rounded-[21px] opacity-0 group-hover:opacity-100 transition-opacity duration-[987ms]">
                  <div className="absolute inset-[1px] rounded-[20px] bg-gradient-to-r from-transparent via-emerald-400/5 to-transparent animate-pulse" style={{animationDuration: '4.236s'}}></div>
                </div>

                <span className="relative z-30 flex items-center justify-center space-x-4">
                  {/* Mobile psychological status indicator */}
                  <div className="relative flex items-center justify-center">
                    <div className="absolute w-6 h-6 bg-emerald-400/20 rounded-full animate-ping opacity-0 group-hover:opacity-100 transition-opacity duration-[610ms]" style={{animationDuration: '3.618s'}}></div>
                    <div className="absolute w-4 h-4 bg-emerald-300/30 rounded-full animate-ping opacity-0 group-hover:opacity-100 transition-opacity duration-[377ms] delay-[233ms]" style={{animationDuration: '2.618s'}}></div>

                    <div className="relative w-[13px] h-[13px] bg-gradient-to-br from-emerald-300 via-emerald-400 to-emerald-500 rounded-full shadow-[0_0_20px_rgba(16,185,129,0.6)] group-hover:shadow-[0_0_30px_rgba(16,185,129,0.8)] transition-all duration-[377ms]">
                      <div className="absolute inset-[2px] bg-gradient-to-br from-white/90 via-emerald-100/80 to-white/70 rounded-full group-hover:from-white group-hover:via-white/90 group-hover:to-emerald-50 transition-all duration-[233ms]"></div>
                      <div className="absolute inset-[4px] bg-emerald-200 rounded-full opacity-80 group-hover:opacity-100 animate-pulse transition-opacity duration-[144ms]" style={{animationDuration: '1.618s'}}></div>
                    </div>
                  </div>

                  {/* Mobile optimized typography */}
                  <div className="relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-emerald-100 via-white to-emerald-100 bg-clip-text text-transparent blur-[1px] opacity-0 group-hover:opacity-30 transition-opacity duration-[377ms]">
                      {t('header.joinUs')}
                    </div>

                    <span className={cn(
                      "relative font-black text-[17px] whitespace-nowrap transition-all duration-[610ms] ease-[cubic-bezier(0.618,0,0.382,1)]",
                      "bg-gradient-to-r from-emerald-50 via-white to-emerald-50",
                      "group-hover:from-white group-hover:via-emerald-25 group-hover:to-white",
                      "bg-clip-text text-transparent",
                      "drop-shadow-[0_1px_3px_rgba(16,185,129,0.3)] group-hover:drop-shadow-[0_2px_8px_rgba(16,185,129,0.4)]",
                      "tracking-[0.618px] group-hover:tracking-[1px]",
                      "transform group-hover:scale-[1.05] transition-transform duration-[377ms]"
                    )}>
                      {t('header.joinUs')}
                    </span>
                  </div>

                  {/* Mobile fibonacci motion indicator */}
                  <div className="relative overflow-hidden w-6 h-4">
                    <div className="absolute inset-0 flex items-center transition-all duration-[377ms] group-hover:translate-x-[2px] group-hover:scale-110">
                      <div className="w-[5px] h-[5px] bg-gradient-to-br from-emerald-300 to-emerald-400 rounded-full opacity-90 group-hover:opacity-100 transition-all duration-[233ms] shadow-[0_0_8px_rgba(16,185,129,0.4)]"></div>
                      <div className="w-[3px] h-[3px] bg-gradient-to-br from-emerald-200 to-emerald-300 rounded-full ml-[3px] opacity-70 group-hover:opacity-95 transition-all duration-[233ms] delay-[89ms] shadow-[0_0_6px_rgba(16,185,129,0.3)]"></div>
                      <div className="w-[2px] h-[2px] bg-gradient-to-br from-emerald-100 to-emerald-200 rounded-full ml-[2px] opacity-50 group-hover:opacity-80 transition-all duration-[233ms] delay-[144ms] shadow-[0_0_4px_rgba(16,185,129,0.2)]"></div>
                    </div>
                  </div>
                </span>

                {/* Mobile neuromorphic holographic field */}
                <div className="absolute -inset-[2px] rounded-[23px] bg-gradient-conic from-emerald-400/0 via-emerald-400/12 via-green-300/15 to-emerald-400/0 opacity-0 group-hover:opacity-100 transition-opacity duration-[987ms] blur-[2px] animate-spin" style={{animationDuration: '25s'}}></div>
                <div className="absolute inset-[1px] rounded-[20px] bg-gradient-to-br from-emerald-500/5 via-transparent to-green-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-[610ms] delay-[377ms]"></div>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
