{"header": {"technology": {"title": "Technologie ACI", "whatIsACI": {"label": "Qu'est-ce que l'ACI ?", "description": "Intelligence Cellulaire Artificielle"}, "coreModels": {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "Algorithmes Bio-Inspirés"}, "technicalFoundation": {"label": "Fondation Technique", "description": "Cadre Mathématique"}, "innovationEngine": {"label": "Moteur d'Innovation", "description": "Évolution Continue"}, "competitiveAdvantage": {"label": "Avantage Concurrentiel", "description": "Pourquoi l'ACI Gagne"}}, "impact": {"title": "Impact", "imperative": {"label": "L'Impératif", "description": "Pourquoi le changement est nécessaire maintenant"}, "globalVision": {"label": "Vision Globale", "description": "Transformation à long terme"}, "globalImpact": {"label": "Impact Global", "description": "Transformation mondiale"}, "aboutUs": {"label": "À Propos", "description": "Notre mission et notre équipe"}}, "divisions": {"title": "Divisions", "symbioCore": {"label": "SymbioCore", "description": "Plateforme d'intelligence centrale", "status": "bientôt-disponible"}, "symbioLabs": {"label": "SymbioLabs", "description": "Centre de recherche et développement", "status": "bientôt-disponible"}, "symbioAutomate": {"label": "SymbioAutomate", "description": "Suite d'automatisation intelligente", "status": "actif"}, "symbioXchange": {"label": "SymbioXchange", "description": "Échange global de ressources", "status": "bientôt-disponible"}, "symbioEdge": {"label": "SymbioEdge", "description": "Intelligence distribuée en périphérie", "status": "bientôt-disponible"}, "symbioImpact": {"label": "SymbioImpact", "description": "Plateforme d'impact global", "status": "bientôt-disponible"}, "symbioVentures": {"label": "SymbioVentures", "description": "Investissement en innovation", "status": "bientôt-disponible"}, "symbioAlliance": {"label": "SymbioAlliance", "description": "Partenariats stratégiques", "status": "bientôt-disponible"}}, "joinUs": "Nous Rejoindre", "comingSoon": "Bientôt Disponible"}, "hero": {"catchyTexts": ["ACI pour un Avenir Symbiotique", "Où l'Intelligence Rencontre l'Évolution", "L'Avenir de la Conscience Artificielle", "Révolutionner l'Intelligence Globale"], "subtitle": "Intelligence Cellulaire Artificielle pour un Avenir Symbiotique", "description": "Nous construisons le premier écosystème d'intelligence symbiotique au monde, où les entreprises fonctionnent comme des organismes vivants en parfaite harmonie avec leur environnement.", "problemStatement": {"title": "12 Billions $ Perdus Annuellement par des Systèmes Inefficaces", "description": "Alors que l'IA traditionnelle fonctionne en isolation, l'ACI crée une intelligence vivante et évolutive qui s'adapte et collabore en temps réel à travers des écosystèmes entiers."}, "ecosystem": {"title": "Entrez dans l'Écosystème Symbiotique", "cards": {"imperative": {"title": "L'Impératif", "description": "Pourquoi la transformation est nécessaire maintenant"}, "aciTechnology": {"title": "Technologie ACI", "description": "L'évolution au-delà de l'IA"}, "flywheel": {"title": "Volant d'Écosystème", "description": "Création de valeur synergique"}, "symbioAutomate": {"title": "SymbioAutomate", "description": "Plateforme d'automatisation en direct"}}}, "cta": {"primary": "Explorer l'Écosystème", "secondary": "Découvrir la Vision"}}, "aci": {"title": "Intelligence Cellulaire Artificielle", "subtitle": "Au-delà de l'IA Conventionnelle : Un Nouveau Paradigme", "description": "Pour alimenter le paradigme symbiotique, une nouvelle forme d'intelligence est requise. L'atout fondamental de SymbioWave est son Intelligence Cellulaire Artificielle (ACI) propriétaire.", "characteristicsTitle": "Caractéristiques Clés de l'ACI", "comparison": {"traditional": {"title": "IA Traditionnelle"}, "aci": {"title": "Intelligence Cellulaire Artificielle"}}, "modelSuite": {"title": "La Suite de Modèles ACI : Les Algorithmes de la Nature pour les Entreprises", "description": "Découvrez notre collection complète de modèles d'IA bio-inspirés qui transforment l'intelligence naturelle en solutions d'affaires. Chaque modèle est inspiré par des millions d'années d'optimisation évolutionnaire."}, "characteristics": {"decentralized": {"title": "Intelligence Décentralisée", "description": "Comme les cellules dans un organisme, les agents ACI opèrent avec une autonomie locale tout en contribuant à une intelligence émergente à l'échelle du système."}, "selfOptimization": {"title": "Auto-Optimisation", "description": "Adaptent continuellement les paramètres et connexions basés sur les flux de données en direct, apprenant sans intervention humaine."}, "emergentBehavior": {"title": "Comportement Émergent", "description": "Des stratégies complexes émergent de règles d'interaction simples, résolvant des problèmes de manières non explicitement programmées."}, "radicalScalability": {"title": "Évolutivité Radicale", "description": "La nature légère et décentralisée s'adapte des capteurs IoT aux chaînes d'approvisionnement mondiales avec une efficacité inégalée."}}}, "globalImpact": {"title": "Impact Global & Vision", "subtitle": "Construire l'Infrastructure Fondamentale de Demain", "description": "L'ambition de SymbioWave s'étend bien au-delà des retours financiers. Nous architecturons les systèmes fondamentaux pour une économie circulaire mondiale où durabilité et rentabilité sont intrinsèquement liées.", "impactAreas": {"autonomousEcosystems": {"title": "Écosystèmes Industriels Autonomes", "description": "Des parcs industriels entiers fonctionnant comme des organismes auto-optimisants où les déchets sont entièrement éliminés."}, "resilientSupplyChains": {"title": "Chaînes d'Approvisionnement Mondiales Résilientes", "description": "Des réseaux qui se reconfigurent dynamiquement en réponse aux perturbations, assurant un flux stable de marchandises."}, "symbioticValue": {"title": "Standard de Valeur Symbiotique", "description": "Un monde où les valorisations d'entreprises dépendent du Score Symbiotique—contribution à la santé de l'écosystème."}, "circularEconomy": {"title": "Fondation de l'Économie Circulaire", "description": "L'infrastructure technologique pour un avenir où la croissance économique favorise l'harmonie écologique."}}, "vision": {"title": "Une Transformation Générationnelle", "description": "La transition d'une économie linéaire vers une économie symbiotique est la plus grande transformation commerciale du 21e siècle. SymbioWave est positionnée pour devenir une entreprise générationnelle—dont le succès est intrinsèquement lié à la création d'un monde plus résilient, efficace et durable pour tous."}, "metrics": {"revenue": {"value": "500M $", "label": "Revenus Annuels Projetés d'ici l'Année 5"}, "gmv": {"value": "10B+ $", "label": "Objectif GMV de la Marketplace"}, "impact": {"value": "Global", "label": "Impact de l'Économie Circulaire"}}}, "callToAction": {"title": "Rejoignez la Révolution Symbiotique", "description": "Nous ne construisons pas seulement une entreprise d'un milliard de dollars ; nous architecturons l'avenir de l'industrie. La transition vers l'intelligence symbiotique se produit maintenant, et les leaders de demain se définissent aujourd'hui.", "buttons": {"primary": "Commencez Votre Transformation", "secondary": "Demander une Discussion de Partenariat"}, "trustIndicators": {"ethics": {"title": "Conseil d'Éthique IA", "description": "Développement IA Responsable"}, "advantage": {"title": "Avantage du Premier Entrant", "description": "Définir la Catégorie"}, "proprietary": {"title": "ACI Propriétaire", "description": "Fossé Technologique Inégalé"}}}, "footer": {"brand": {"tagline": "Intelligence Cellulaire Artificielle pour un Avenir Symbiotique", "description": "Construire le premier écosystème d'intelligence symbiotique au monde où les entreprises opèrent comme des organismes vivants."}, "contact": {"title": "Contact Entreprise", "info": "<EMAIL>", "general": "<EMAIL>"}, "sections": {"ecosystem": {"title": "Écosystème", "symbioCore": "SymbioCore", "symbioLabs": "SymbioLabs", "symbioAutomate": "SymbioAutomate", "symbioXchange": "SymbioXchange"}, "solutions": {"title": "Solutions", "symbioEdge": "SymbioEdge", "symbioImpact": "SymbioImpact", "symbioVentures": "SymbioVentures", "symbioAlliance": "SymbioAlliance"}, "platform": {"title": "Plateforme", "ecosystem": "Écosystème", "integration": "Intégration", "sustainability": "Durabilité"}, "company": {"title": "Entreprise", "documentation": "Documentation", "caseStudies": "Études de Cas", "careers": "Carrières"}}, "copyright": "© {{year}} SymbioWave Corporation. Tous droits réservés.", "poweredBy": "Alimenté par la Technologie ACI"}, "pages": {"coreModels": {"title": "La Suite de Modèles ACI : Les Algorithmes de la Nature pour les Affaires", "subtitle": "Découvrez notre collection complète de modèles d'IA bio-inspirés qui transforment l'intelligence naturelle en solutions commerciales.", "description": "<PERSON><PERSON> modèle de notre suite s'inspire de millions d'années d'optimisation évolutionnaire, apportant les algorithmes les plus efficaces de la nature pour résoudre les défis commerciaux modernes.", "models": {"physarum": {"name": "Optimiseur de Réseau Inspiré du Physarum", "description": "Imite l'intelligence du blob pour concevoir les réseaux logistiques, routes de données et grilles énergétiques les plus efficaces et résilients.", "useCases": ["Optimisation de chaîne d'approvisionnement mondiale", "Gestion du trafic de ville intelligente", "Distribution de grille énergétique"]}, "quorum": {"name": "Moteur de Consensus par Détection de Quorum", "description": "Utilise les principes de communication bactérienne pour l'accord décentralisé entre agents autonomes.", "useCases": ["Coordination de flotte de drones", "Gestion d'<PERSON><PERSON><PERSON><PERSON><PERSON> intelligent", "Ressources énergétiques distribuées"]}, "immune": {"name": "Détecteur d'Anomalies Inspiré du Système Immunitaire", "description": "Applique les principes du système immunitaire pour identifier les menaces et anomalies dans les systèmes complexes.", "useCases": ["Détection de menaces cybersécurité", "Contrôle qualité en fabrication", "Prévention de fraude financière"]}, "antColony": {"name": "Optimiseur Inspiré des Colonies de Fourmis", "description": "Exploite l'intelligence collective des essaims pour résoudre des problèmes d'optimisation complexes.", "useCases": ["Optimisation d'itinéraires", "Allocation de ressources", "Optimisation de planification"]}}, "categories": {"networkIntelligence": {"title": "Intelligence de Réseau", "description": "Modèles bio-inspirés pour optimiser les réseaux et connexions complexes"}, "adaptiveDefense": {"title": "Défense Adaptative", "description": "Modèles inspirés du système immunitaire pour la protection et détection d'anomalies"}, "optimizationEngines": {"title": "Moteurs d'Optimisation", "description": "Modèles d'intelligence d'essaim pour résoudre des problèmes d'optimisation complexes"}}, "cta": {"title": "<PERSON>r<PERSON><PERSON> à Exploiter l'Intelligence de la Nature ?", "description": "Nos modèles ACI sont disponibles via SymbioCore, notre plateforme cloud qui fournit un accès API transparent à la suite complète d'intelligence bio-inspirée.", "buttons": {"primary": "Accéder à SymbioCore", "secondary": "<PERSON><PERSON><PERSON> une Démo"}}}, "innovationEngine": {"title": "Moteur d'Innovation - Évolution Continue", "subtitle": "Intelligence Auto-Améliorante : L'Avantage SymbioLabs", "description": "Alors que l'IA traditionnelle nécessite un réentraînement coûteux, l'ACI évolue continuellement. Notre Moteur d'Innovation garantit que chaque interaction, chaque point de données et chaque transaction rend l'ensemble de l'écosystème plus intelligent."}, "competitiveAdvantage": {"title": "Pourquoi l'ACI Gagne", "subtitle": "Les Avantages Concurrentiels Qui Rendent SymbioWave Imbattable", "description": "Notre approche bio-inspirée crée plusieurs couches d'avantages concurrentiels que l'IA traditionnelle ne peut tout simplement pas reproduire.", "moats": {"biologicalInspiration": {"title": "Fossé d'Inspiration Biologique", "description": "4 milliards d'années d'optimisation évolutionnaire ne peuvent être reproduites par les approches traditionnelles", "advantages": ["Algorithmes éprouvés testés par l'évolution", "Résilience et adaptabilité naturelles", "Propriétés d'intelligence émergente", "Capacités d'auto-organisation"]}, "technicalFoundation": {"title": "Fossé de Fondation Technique", "description": "Notre cadre mathématique crée des barrières que les concurrents ne peuvent facilement surmonter", "advantages": ["Modèles bio-mathématiques propriétaires", "Systèmes d'automates cellulaires avancés", "Optimisation inspirée quantique", "Capacités d'intégration multi-échelle"]}, "innovationEngine": {"title": "Fossé du Moteur d'Innovation", "description": "Évolution continue grâce à l'apprentissage bio-inspiré nous garde en avance", "advantages": ["Algorithmes auto-améliorants", "Évolution adaptative des modèles", "Optimisation en temps réel", "Découverte de capacités émergentes"]}}, "comparison": {"title": "ACI vs IA Traditionnelle", "subtitle": "<PERSON>oyez comment notre approche bio-inspirée surpasse les méthodes conventionnelles", "categories": {"adaptability": {"title": "Adaptabilité", "aci": "Algorithmes auto-évolutifs qui s'adaptent automatiquement aux nouvelles conditions", "traditional": "Nécessite un réentraînement manuel et une intervention humaine"}, "efficiency": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aci": "Algorithmes optimisés par la nature avec une surcharge computationnelle minimale", "traditional": "Approches computationnelles de force brute intensives en ressources"}, "resilience": {"title": "Résilience", "aci": "Tolérance aux pannes intégrée et capacités d'auto-guérison", "traditional": "Systèmes fragiles sujets aux défaillances en cascade"}, "scalability": {"title": "Évolutivité", "aci": "Modèles de croissance organique qui s'adaptent naturellement", "traditional": "Mise à l'échelle linéaire avec rendements décroissants"}}}, "cta": {"title": "Prêt à Expérimenter l'Avantage ACI ?", "description": "Rejoignez les entreprises qui exploitent déjà l'intelligence bio-inspirée pour transformer leurs opérations.", "buttons": {"primary": "Commencer Votre Parcours ACI", "secondary": "Planifier une Consultation"}}}, "interface": {"title": "Interface SymbioWave", "subtitle": "Votre Passerelle vers l'Écosystème Symbiotique", "description": "Découvrez l'avenir des opérations commerciales intelligentes grâce à notre interface unifiée.", "features": {"unifiedDashboard": {"title": "Tableau de Bord Unifié", "description": "Surveillez toutes les divisions SymbioWave depuis une interface unique et intelligente"}, "aciIntegration": {"title": "Intégration ACI", "description": "Accès direct aux capacités d'Intelligence Cellulaire Artificielle"}, "realTimeAnalytics": {"title": "Analy<PERSON> en Temps Réel", "description": "Insights en direct sur l'ensemble de votre écosystème symbiotique"}, "advancedSecurity": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Communications chiffrées quantiques et accès bio-vérifié"}}}, "documentation": {"title": "Documentation", "subtitle": "Tout ce dont vous avez besoin pour construire, déployer et faire évoluer avec SymbioWave", "sections": {"gettingStarted": {"title": "Commencer", "description": "Configuration rapide et concepts de base", "items": ["Installation", "Configuration", "Premiers Pa<PERSON>", "Exemples de Base"]}, "apiReference": {"title": "Référence API", "description": "Documentation API complète", "items": ["Authentification", "Points de Terminaison", "Paramètres", "Formats de Réponse"]}, "integrations": {"title": "Intégrations", "description": "Connectez-vous avec vos outils existants", "items": ["APIs Tierces", "Webhooks", "Connecteurs Personnalisés", "Sources de Données"]}, "bestPractices": {"title": "Meilleures Pratiques", "description": "Conseils d'optimisation et modèles", "items": ["Conception de Flux de Travail", "Optimisation des Performances", "Directives de Sécurité", "Dépannage"]}}}, "vision": {"title": "Vision Globale", "subtitle": "L'Avenir que Nous Construisons Ensemble", "description": "Un monde où la technologie sert l'humanité grâce à l'intelligence symbiotique.", "futureScenarios": {"healthcareRevolution": {"title": "Révolution des Soins de Santé", "description": "Les systèmes ACI découvrent des traitements personnalisés en analysant les modèles à travers des millions d'interactions cellulaires.", "impact": "Médecine personnalisée pour tous"}, "climateOptimization": {"title": "Optimisation Climatique", "description": "Les réseaux symbiotiques coordonnent les ressources mondiales en temps réel, optimisant pour la durabilité et l'efficacité.", "impact": "Neutralité carbone d'ici 2030"}, "economicSymbiosis": {"title": "Symbiose Économique", "description": "Les entreprises opèrent comme des écosystèmes vivants, où la valeur circule naturellement là où elle est le plus nécessaire.", "impact": "Économie post-rareté"}}}}, "technical": {"apiIntegration": {"title": "Intégration API", "description": "APIs RESTful et points de terminaison GraphQL pour une intégration transparente avec les systèmes existants.", "details": ["API REST v2.0", "Support GraphQL", "Notifications webhook", "Bibliothèques SDK"]}, "dataArchitecture": {"title": "Architecture de Données", "description": "Traitement de données sécurisé et évolutif avec analyses en temps réel et apprentissage automatique.", "details": ["Traitement en temps réel", "Pipelines ML", "Chiffrement des données", "Conforme RGPD"]}, "aciModels": {"title": "Modèles ACI", "description": "Alimenté par les algorithmes avancés d'Intelligence Cellulaire Artificielle de SymbioCore.", "details": ["Optimiseur Physarum", "Détection de quorum", "Logique de colonie de fourmis", "Détection immunitaire"]}}, "common": {"buttons": {"learnMore": "En Savoir Plus", "getStarted": "Commencer", "requestDemo": "<PERSON><PERSON><PERSON> une Démo", "contactUs": "<PERSON><PERSON>", "exploreMore": "Explorer Plus", "joinNow": "Rejoindre Maintenant", "startTrial": "Commencer l'Essai", "viewDetails": "Voir les Détails", "readMore": "Lire Plus", "downloadNow": "Télécharger Maintenant"}, "status": {"active": "Actif", "comingSoon": "Bientôt Disponible", "inDevelopment": "En Développement", "beta": "<PERSON><PERSON><PERSON>", "alpha": "Alpha"}, "navigation": {"home": "Accueil", "back": "Retour", "next": "Suivant", "previous": "Précédent", "close": "<PERSON><PERSON><PERSON>", "menu": "<PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>"}, "forms": {"name": "Nom", "email": "Email", "company": "Entreprise", "message": "Message", "phone": "Téléphone", "submit": "So<PERSON><PERSON><PERSON>", "cancel": "Annuler", "required": "Requis", "optional": "Optionnel", "pleaseWait": "Veuillez patienter...", "success": "Succès !", "error": "Une erreur s'est produite", "thankYou": "Merci pour votre intérêt !"}, "loading": "Chargement...", "error": "Une erreur s'est produite", "retry": "<PERSON><PERSON><PERSON><PERSON>", "noData": "<PERSON><PERSON><PERSON> donnée disponible"}, "language": {"english": "English", "french": "Français", "switchTo": "Passer à {{language}}"}}