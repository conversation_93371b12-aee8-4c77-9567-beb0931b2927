
import React from 'react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Card from '../components/atoms/Card';
import Button from '../components/atoms/Button';
import { Brain, Zap, Network, Cpu, ArrowRight } from 'lucide-react';

const ACITechnology: React.FC = () => {
  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main className="pt-24">
        <div className="container mx-auto px-8 py-16">
          <div className="text-center mb-16">
            <Typography as="h1" variant="4xl" weight="bold" gradient="consciousness" className="mb-6">
              Artificial Cellular Intelligence
            </Typography>
            <Typography as="p" variant="xl" color="secondary" className="max-w-4xl mx-auto">
              Discover the revolutionary AI paradigm that's reshaping how intelligent systems learn, adapt, and evolve.
            </Typography>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            <Card
              variant="consciousness"
              className="p-8 text-center rounded-xl border-consciousness/25 cursor-pointer hover:border-consciousness/50 transition-all duration-300"
              onClick={() => window.location.href = '/technical-foundation'}
            >
              <Brain className="w-16 h-16 mx-auto mb-6 text-consciousness" />
              <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
                Technical Foundation
              </Typography>
              <Typography variant="sm" color="secondary">
                Mathematical framework and core principles
              </Typography>
            </Card>

            <Card
              variant="creativity"
              className="p-8 text-center rounded-xl border-creativity/25 cursor-pointer hover:border-creativity/50 transition-all duration-300"
              onClick={() => window.location.href = '/innovation-engine'}
            >
              <Network className="w-16 h-16 mx-auto mb-6 text-creativity" />
              <Typography variant="lg" weight="semibold" color="creativity" className="mb-4">
                Innovation Engine
              </Typography>
              <Typography variant="sm" color="secondary">
                Continuous evolution and learning
              </Typography>
            </Card>

            <Card
              variant="intuition"
              className="p-8 text-center rounded-xl border-harmony/25 cursor-pointer hover:border-harmony/50 transition-all duration-300"
              onClick={() => window.location.href = '/competitive-advantage'}
            >
              <Cpu className="w-16 h-16 mx-auto mb-6 text-harmony" />
              <Typography variant="lg" weight="semibold" color="harmony" className="mb-4">
                Competitive Advantage
              </Typography>
              <Typography variant="sm" color="secondary">
                Why ACI wins over traditional AI
              </Typography>
            </Card>

            <Card
              variant="neural"
              className="p-8 text-center rounded-xl border-consciousness/25 cursor-pointer hover:border-consciousness/50 transition-all duration-300"
              onClick={() => window.location.href = '/quantum-processing'}
            >
              <Zap className="w-16 h-16 mx-auto mb-6 text-consciousness" />
              <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
                Quantum Processing
              </Typography>
              <Typography variant="sm" color="secondary">
                Advanced computational methods
              </Typography>
            </Card>
          </div>

          <div className="glass-quantum rounded-[32px] p-12 border border-consciousness/30">
            <Typography as="h2" variant="2xl" weight="semibold" color="consciousness" className="mb-8">
              The Future of Intelligent Systems
            </Typography>
            <Typography variant="lg" color="secondary" className="leading-relaxed mb-8">
              ACI represents a fundamental shift from traditional AI architectures. Instead of monolithic models,
              ACI creates ecosystems of intelligent agents that collaborate, compete, and evolve—just like biological cells.
              This approach enables unprecedented adaptability, resilience, and emergent intelligence.
            </Typography>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Button
                variant="quantum"
                size="xl"
                leftIcon={<Brain className="w-6 h-6" />}
                rightIcon={<ArrowRight className="w-5 h-5" />}
                onClick={() => window.location.href = '/technical-foundation'}
                className="min-w-[200px] font-semibold"
              >
                Technical Foundation
              </Button>

              <Button
                variant="professional"
                size="xl"
                leftIcon={<Zap className="w-6 h-6" />}
                onClick={() => window.location.href = '/innovation-engine'}
                className="min-w-[200px] font-semibold"
              >
                Innovation Engine
              </Button>

              <Button
                variant="professional"
                size="xl"
                leftIcon={<Cpu className="w-6 h-6" />}
                onClick={() => window.location.href = '/competitive-advantage'}
                className="min-w-[200px] font-semibold"
              >
                Competitive Advantage
              </Button>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default ACITechnology;
