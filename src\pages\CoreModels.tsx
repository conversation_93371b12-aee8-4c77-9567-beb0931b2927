import React, { useState } from 'react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Card from '../components/atoms/Card';
import Button from '../components/atoms/Button';
import { 
  Brain, 
  Zap, 
  Network, 
  Shield, 
  Cpu, 
  Target,
  GitBranch,
  Activity 
} from 'lucide-react';

const CoreModels: React.FC = () => {
  const [activeModel, setActiveModel] = useState(0);

  const aciModels = [
    {
      name: "Physarum-Inspired Network Optimizer",
      description: "Mimics slime mold intelligence to design the most efficient and resilient logistics networks, data routes, and energy grids.",
      use_cases: ["Global supply chain optimization", "Smart city traffic management", "Energy grid distribution"],
      color: "consciousness"
    },
    {
      name: "Quorum-Sensing Consensus Engine", 
      description: "Uses bacterial communication principles for decentralized agreement among autonomous agents.",
      use_cases: ["Drone fleet coordination", "Smart warehouse management", "Distributed energy resources"],
      color: "creativity"
    },
    {
      name: "Immune-Inspired Anomaly Detector",
      description: "Functions like a biological immune system to identify and neutralize threats in real-time.",
      use_cases: ["Cybersecurity defense", "Financial fraud detection", "Predictive maintenance"],
      color: "intuition"
    },
    {
      name: "Ant Colony-Inspired Optimizer",
      description: "Solves complex combinatorial problems by simulating emergent ant foraging intelligence.",
      use_cases: ["Dynamic job scheduling", "Route optimization", "Resource allocation"],
      color: "harmony"
    },
    {
      name: "Stem Cell-Inspired Role Allocator",
      description: "Dynamically assigns tasks and resources based on real-time needs, mirroring cellular differentiation.",
      use_cases: ["Adaptive workforce management", "Dynamic pricing", "Capacity planning"],
      color: "transcendence"
    },
    {
      name: "Cancer-Inspired Explorer",
      description: "Leverages aggressive adaptation strategies to identify and exploit new opportunities.",
      use_cases: ["Market opportunity discovery", "Competitive landscape navigation", "Innovation scouting"],
      color: "consciousness"
    }
  ];

  const modelCategories = [
    {
      icon: Network,
      title: "Network Intelligence",
      description: "Bio-inspired models for optimizing complex networks and connections",
      color: "consciousness",
      models: ["Physarum-Inspired Network Optimizer", "Quorum-Sensing Consensus Engine"]
    },
    {
      icon: Shield,
      title: "Adaptive Defense", 
      description: "Immune system-inspired models for protection and anomaly detection",
      color: "creativity",
      models: ["Immune-Inspired Anomaly Detector"]
    },
    {
      icon: Target,
      title: "Optimization Engines",
      description: "Swarm intelligence models for solving complex optimization problems",
      color: "intuition",
      models: ["Ant Colony-Inspired Optimizer"]
    },
    {
      icon: GitBranch,
      title: "Dynamic Allocation",
      description: "Cellular differentiation models for adaptive resource management",
      color: "harmony",
      models: ["Stem Cell-Inspired Role Allocator", "Cancer-Inspired Explorer"]
    }
  ];

  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main className="pt-24">
        <div className="container mx-auto px-8 py-16">
          <div className="text-center mb-16">
            <Typography as="h1" variant="4xl" weight="bold" gradient="neural" className="mb-6">
              The ACI Model Suite: Nature's Algorithms for Business
            </Typography>
            <Typography as="p" variant="xl" color="secondary" className="max-w-4xl mx-auto mb-8">
              Discover our comprehensive collection of bio-inspired AI models that transform natural intelligence into business solutions.
            </Typography>
            <Typography as="p" variant="lg" color="tertiary" className="max-w-3xl mx-auto">
              Each model in our suite is inspired by millions of years of evolutionary optimization, 
              bringing nature's most efficient algorithms to solve modern business challenges.
            </Typography>
          </div>

          {/* Model Categories Overview */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {modelCategories.map((category, index) => {
              const IconComponent = category.icon;
              return (
                <Card 
                  key={category.title}
                  variant="neural"
                  className={`group text-center hover:border-${category.color}/50 transition-all duration-bio hover:transform hover:scale-105`}
                >
                  <div className={`w-16 h-16 mx-auto mb-4 rounded-cellular bg-${category.color}/10 flex items-center justify-center group-hover:animate-cellular-flow`}>
                    <IconComponent className={`w-8 h-8 text-${category.color}`} />
                  </div>
                  <Typography variant="lg" weight="semibold" className={`mb-3 text-${category.color}`}>
                    {category.title}
                  </Typography>
                  <Typography variant="xs" color="secondary" className="leading-relaxed mb-4">
                    {category.description}
                  </Typography>
                  <Typography variant="micro" color="tertiary">
                    {category.models.length} model{category.models.length > 1 ? 's' : ''}
                  </Typography>
                </Card>
              );
            })}
          </div>

          {/* Interactive Model Explorer */}
          <div className="mb-16">
            <Typography 
              as="h2" 
              variant="2xl" 
              weight="bold" 
              color="consciousness"
              align="center"
              className="mb-12"
            >
              Explore Individual Models
            </Typography>

            <div className="grid lg:grid-cols-3 gap-8">
              {/* Model Navigation */}
              <div className="lg:col-span-1">
                <Card variant="neural" className="p-6">
                  <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
                    Select ACI Model
                  </Typography>
                  <div className="space-y-2">
                    {aciModels.map((model, index) => (
                      <button
                        key={model.name}
                        onClick={() => setActiveModel(index)}
                        className={`w-full text-left p-3 rounded-lg transition-all duration-quantum ${
                          activeModel === index 
                            ? `bg-${model.color}/20 border border-${model.color}/40` 
                            : 'hover:bg-white/5'
                        }`}
                      >
                        <Typography 
                          variant="sm" 
                          weight="medium" 
                          className={activeModel === index ? `text-${model.color}` : 'text-secondary'}
                        >
                          {model.name}
                        </Typography>
                      </button>
                    ))}
                  </div>
                </Card>
              </div>

              {/* Active Model Details */}
              <div className="lg:col-span-2">
                <Card variant="quantum" className={`border-${aciModels[activeModel].color}/30`}>
                  <Typography 
                    variant="xl" 
                    weight="bold" 
                    className={`mb-4 text-${aciModels[activeModel].color}`}
                  >
                    {aciModels[activeModel].name}
                  </Typography>

                  <Typography 
                    variant="lg" 
                    color="secondary" 
                    className="mb-6 leading-relaxed"
                  >
                    {aciModels[activeModel].description}
                  </Typography>

                  <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
                    Use Cases:
                  </Typography>

                  <div className="grid md:grid-cols-1 gap-3 mb-8">
                    {aciModels[activeModel].use_cases.map((useCase, index) => (
                      <div key={useCase} className="flex items-center space-x-3">
                        <div className={`w-2 h-2 bg-${aciModels[activeModel].color}/70 rounded-full flex-shrink-0`}></div>
                        <Typography variant="sm" color="secondary">{useCase}</Typography>
                      </div>
                    ))}
                  </div>

                  <div className="flex flex-col sm:flex-row gap-4">
                    <Button 
                      variant="quantum" 
                      size="lg"
                      className="group"
                    >
                      <span className="group-hover:text-consciousness transition-colors duration-quantum">
                        Learn More About This Model
                      </span>
                    </Button>
                    <Button 
                      variant="outline-quantum" 
                      size="lg"
                    >
                      View Technical Details
                    </Button>
                  </div>
                </Card>
              </div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="glass-quantum rounded-[32px] p-12 border border-consciousness/30 text-center">
            <Typography as="h2" variant="2xl" weight="semibold" color="consciousness" className="mb-8">
              Ready to Harness Nature's Intelligence?
            </Typography>
            <Typography variant="lg" color="secondary" className="leading-relaxed mb-8 max-w-3xl mx-auto">
              Our ACI models are available through SymbioCore, our cloud-based platform that provides 
              seamless API access to the full suite of bio-inspired intelligence.
            </Typography>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Button
                variant="quantum"
                size="xl"
                leftIcon={<Brain className="w-6 h-6" />}
                onClick={() => window.location.href = '/symbiocore'}
                className="min-w-[200px] font-semibold"
              >
                Access via SymbioCore
              </Button>
              <Button
                variant="professional"
                size="xl"
                leftIcon={<Zap className="w-6 h-6" />}
                onClick={() => window.location.href = '/request-demo'}
                className="min-w-[200px] font-semibold"
              >
                Request Demo
              </Button>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default CoreModels;
