import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Card from '../components/atoms/Card';
import Button from '../components/atoms/Button';
import {
  Brain,
  Zap,
  Network,
  Shield,
  Cpu,
  Target,
  GitBranch,
  Activity
} from 'lucide-react';

const CoreModels: React.FC = () => {
  const { t } = useTranslation();
  const [activeModel, setActiveModel] = useState(0);

  const aciModels = [
    {
      name: t('pages.coreModels.models.physarum.name'),
      description: t('pages.coreModels.models.physarum.description'),
      use_cases: t('pages.coreModels.models.physarum.useCases', { returnObjects: true }) as string[],
      color: "consciousness"
    },
    {
      name: t('pages.coreModels.models.quorum.name'),
      description: t('pages.coreModels.models.quorum.description'),
      use_cases: t('pages.coreModels.models.quorum.useCases', { returnObjects: true }) as string[],
      color: "creativity"
    },
    {
      name: t('pages.coreModels.models.immune.name'),
      description: t('pages.coreModels.models.immune.description'),
      use_cases: t('pages.coreModels.models.immune.useCases', { returnObjects: true }) as string[],
      color: "intuition"
    },
    {
      name: t('pages.coreModels.models.antColony.name'),
      description: t('pages.coreModels.models.antColony.description'),
      use_cases: t('pages.coreModels.models.antColony.useCases', { returnObjects: true }) as string[],
      color: "harmony"
    }
  ];

  const modelCategories = [
    {
      icon: Network,
      title: t('pages.coreModels.categories.networkIntelligence.title'),
      description: t('pages.coreModels.categories.networkIntelligence.description'),
      color: "consciousness",
      models: [t('pages.coreModels.models.physarum.name'), t('pages.coreModels.models.quorum.name')]
    },
    {
      icon: Shield,
      title: t('pages.coreModels.categories.adaptiveDefense.title'),
      description: t('pages.coreModels.categories.adaptiveDefense.description'),
      color: "creativity",
      models: [t('pages.coreModels.models.immune.name')]
    },
    {
      icon: Target,
      title: t('pages.coreModels.categories.optimizationEngines.title'),
      description: t('pages.coreModels.categories.optimizationEngines.description'),
      color: "intuition",
      models: [t('pages.coreModels.models.antColony.name')]
    }
  ];

  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main className="pt-24">
        <div className="container mx-auto px-8 py-16">
          <div className="text-center mb-16">
            <Typography as="h1" variant="4xl" weight="bold" gradient="neural" className="mb-6">
              {t('pages.coreModels.title')}
            </Typography>
            <Typography as="p" variant="xl" color="secondary" className="max-w-4xl mx-auto mb-8">
              {t('pages.coreModels.subtitle')}
            </Typography>
            <Typography as="p" variant="lg" color="tertiary" className="max-w-3xl mx-auto">
              {t('pages.coreModels.description')}
            </Typography>
          </div>

          {/* Model Categories Overview */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {modelCategories.map((category, index) => {
              const IconComponent = category.icon;
              return (
                <Card 
                  key={category.title}
                  variant="neural"
                  className={`group text-center hover:border-${category.color}/50 transition-all duration-bio hover:transform hover:scale-105`}
                >
                  <div className={`w-16 h-16 mx-auto mb-4 rounded-cellular bg-${category.color}/10 flex items-center justify-center group-hover:animate-cellular-flow`}>
                    <IconComponent className={`w-8 h-8 text-${category.color}`} />
                  </div>
                  <Typography variant="lg" weight="semibold" className={`mb-3 text-${category.color}`}>
                    {category.title}
                  </Typography>
                  <Typography variant="xs" color="secondary" className="leading-relaxed mb-4">
                    {category.description}
                  </Typography>
                  <Typography variant="micro" color="tertiary">
                    {category.models.length} model{category.models.length > 1 ? 's' : ''}
                  </Typography>
                </Card>
              );
            })}
          </div>

          {/* Interactive Model Explorer */}
          <div className="mb-16">
            <Typography 
              as="h2" 
              variant="2xl" 
              weight="bold" 
              color="consciousness"
              align="center"
              className="mb-12"
            >
              Explore Individual Models
            </Typography>

            <div className="grid lg:grid-cols-3 gap-8">
              {/* Model Navigation */}
              <div className="lg:col-span-1">
                <Card variant="neural" className="p-6">
                  <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
                    Select ACI Model
                  </Typography>
                  <div className="space-y-2">
                    {aciModels.map((model, index) => (
                      <button
                        key={model.name}
                        onClick={() => setActiveModel(index)}
                        className={`w-full text-left p-3 rounded-lg transition-all duration-quantum ${
                          activeModel === index 
                            ? `bg-${model.color}/20 border border-${model.color}/40` 
                            : 'hover:bg-white/5'
                        }`}
                      >
                        <Typography 
                          variant="sm" 
                          weight="medium" 
                          className={activeModel === index ? `text-${model.color}` : 'text-secondary'}
                        >
                          {model.name}
                        </Typography>
                      </button>
                    ))}
                  </div>
                </Card>
              </div>

              {/* Active Model Details */}
              <div className="lg:col-span-2">
                <Card variant="quantum" className={`border-${aciModels[activeModel].color}/30`}>
                  <Typography 
                    variant="xl" 
                    weight="bold" 
                    className={`mb-4 text-${aciModels[activeModel].color}`}
                  >
                    {aciModels[activeModel].name}
                  </Typography>

                  <Typography 
                    variant="lg" 
                    color="secondary" 
                    className="mb-6 leading-relaxed"
                  >
                    {aciModels[activeModel].description}
                  </Typography>

                  <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
                    Use Cases:
                  </Typography>

                  <div className="grid md:grid-cols-1 gap-3 mb-8">
                    {aciModels[activeModel].use_cases.map((useCase, index) => (
                      <div key={useCase} className="flex items-center space-x-3">
                        <div className={`w-2 h-2 bg-${aciModels[activeModel].color}/70 rounded-full flex-shrink-0`}></div>
                        <Typography variant="sm" color="secondary">{useCase}</Typography>
                      </div>
                    ))}
                  </div>

                  <div className="flex flex-col sm:flex-row gap-4">
                    <Button 
                      variant="quantum" 
                      size="lg"
                      className="group"
                    >
                      <span className="group-hover:text-consciousness transition-colors duration-quantum">
                        Learn More About This Model
                      </span>
                    </Button>
                    <Button 
                      variant="outline-quantum" 
                      size="lg"
                    >
                      View Technical Details
                    </Button>
                  </div>
                </Card>
              </div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="glass-quantum rounded-[32px] p-12 border border-consciousness/30 text-center">
            <Typography as="h2" variant="2xl" weight="semibold" color="consciousness" className="mb-8">
              {t('pages.coreModels.cta.title')}
            </Typography>
            <Typography variant="lg" color="secondary" className="leading-relaxed mb-8 max-w-3xl mx-auto">
              {t('pages.coreModels.cta.description')}
            </Typography>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Button
                variant="quantum"
                size="xl"
                leftIcon={<Brain className="w-6 h-6" />}
                onClick={() => window.location.href = '/symbiocore'}
                className="min-w-[200px] font-semibold"
              >
                {t('pages.coreModels.cta.buttons.primary')}
              </Button>
              <Button
                variant="professional"
                size="xl"
                leftIcon={<Zap className="w-6 h-6" />}
                onClick={() => window.location.href = '/request-demo'}
                className="min-w-[200px] font-semibold"
              >
                {t('pages.coreModels.cta.buttons.secondary')}
              </Button>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default CoreModels;
