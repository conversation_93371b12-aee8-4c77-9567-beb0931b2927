
import React from 'react';
import Header from '../components/organisms/Header';
import HeroSection from '../components/features/HeroSection';

import ACITechnology from '../components/features/ACITechnology';
import GlobalImpact from '../components/features/GlobalImpact';
import CallToAction from '../components/features/CallToAction';
import Footer from '../components/organisms/Footer';

const Index: React.FC = () => {
  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main>
        {/* The Genesis Portal */}
        <HeroSection />

        {/* ACI Technology Introduction */}
        <ACITechnology />

        {/* Global Impact & Vision */}
        <GlobalImpact />
        
        {/* Final CTA */}
        <CallToAction />
      </main>

      <Footer />
    </div>
  );
};

export default Index;
