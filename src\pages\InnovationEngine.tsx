import React from 'react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Card from '../components/atoms/Card';
import Button from '../components/atoms/Button';
import { 
  Brain, 
  Zap, 
  Network, 
  TrendingUp, 
  ArrowRight,
  RefreshCw,
  Lightbulb,
  Target,
  BarChart3,
  Clock,
  Database,
  CheckCircle,
  Microscope,
  Award,
  Users,
  Globe,
  BookOpen,
  Cpu
} from 'lucide-react';

const InnovationEngine: React.FC = () => {
  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main className="pt-24">
        {/* Hero Section */}
        <div className="container mx-auto px-8 py-16">
          <div className="text-center mb-16">
            <Typography as="h1" variant="4xl" weight="bold" gradient="consciousness" className="mb-6">
              Innovation Engine - Continuous Evolution
            </Typography>
            <Typography as="p" variant="xl" color="secondary" className="max-w-4xl mx-auto mb-8">
              Self-Improving Intelligence: The SymbioLabs Advantage
            </Typography>
            <Typography as="p" variant="lg" color="tertiary" className="max-w-5xl mx-auto leading-relaxed">
              While traditional AI requires expensive retraining, ACI evolves continuously. Our Innovation Engine ensures that 
              every interaction, every data point, and every transaction makes the entire ecosystem smarter.
            </Typography>
          </div>

          {/* Continuous Evolution Paradigm */}
          <Card variant="quantum" className="mb-16 p-12 border-consciousness/30">
            <Typography as="h2" variant="3xl" weight="bold" color="consciousness" className="mb-8">
              The Continuous Evolution Paradigm
            </Typography>
            
            <Card variant="neural" className="mb-8 p-8 border-consciousness/20">
              <Typography as="h3" variant="2xl" weight="semibold" color="consciousness" className="mb-6">
                Real-Time Learning Architecture
              </Typography>
              <Typography variant="lg" color="secondary" className="mb-6">
                Beyond Static Models
              </Typography>
              
              <div className="grid md:grid-cols-2 gap-8 mb-8">
                <div>
                  <Typography variant="lg" weight="semibold" color="creativity" className="mb-4">
                    Traditional AI Pipeline:
                  </Typography>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <Typography variant="sm" color="secondary">Data Collection</Typography>
                    </div>
                    <div className="flex items-center gap-3 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <Typography variant="sm" color="secondary">Training</Typography>
                    </div>
                    <div className="flex items-center gap-3 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <Typography variant="sm" color="secondary">Deployment</Typography>
                    </div>
                    <div className="flex items-center gap-3 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <Typography variant="sm" color="secondary">[Static Operation]</Typography>
                    </div>
                    <div className="flex items-center gap-3 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <Typography variant="sm" color="secondary">Expensive Retraining</Typography>
                    </div>
                  </div>
                </div>

                <div>
                  <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
                    ACI Pipeline:
                  </Typography>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3 p-3 bg-consciousness/10 border border-consciousness/20 rounded-lg">
                      <div className="w-3 h-3 bg-consciousness rounded-full"></div>
                      <Typography variant="sm" color="secondary">Live Data Streams</Typography>
                    </div>
                    <div className="flex items-center gap-3 p-3 bg-consciousness/10 border border-consciousness/20 rounded-lg">
                      <div className="w-3 h-3 bg-consciousness rounded-full"></div>
                      <Typography variant="sm" color="secondary">Continuous Adaptation</Typography>
                    </div>
                    <div className="flex items-center gap-3 p-3 bg-consciousness/10 border border-consciousness/20 rounded-lg">
                      <div className="w-3 h-3 bg-consciousness rounded-full"></div>
                      <Typography variant="sm" color="secondary">Dynamic Optimization</Typography>
                    </div>
                    <div className="flex items-center gap-3 p-3 bg-consciousness/10 border border-consciousness/20 rounded-lg">
                      <div className="w-3 h-3 bg-consciousness rounded-full"></div>
                      <Typography variant="sm" color="secondary">Enhanced Performance</Typography>
                    </div>
                  </div>
                </div>
              </div>

              <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
                Key Innovations:
              </Typography>
              
              <div className="grid md:grid-cols-3 gap-6">
                <Card variant="consciousness" className="p-6 border-consciousness/25">
                  <RefreshCw className="w-8 h-8 text-consciousness mb-4" />
                  <Typography variant="lg" weight="semibold" color="consciousness" className="mb-2">
                    Online Learning
                  </Typography>
                  <Typography variant="sm" color="secondary">
                    Models adapt in real-time to new patterns
                  </Typography>
                </Card>

                <Card variant="creativity" className="p-6 border-creativity/25">
                  <Network className="w-8 h-8 text-creativity mb-4" />
                  <Typography variant="lg" weight="semibold" color="creativity" className="mb-2">
                    Transfer Learning
                  </Typography>
                  <Typography variant="sm" color="secondary">
                    Knowledge gained in one domain enhances others
                  </Typography>
                </Card>

                <Card variant="intuition" className="p-6 border-harmony/25">
                  <Brain className="w-8 h-8 text-harmony mb-4" />
                  <Typography variant="lg" weight="semibold" color="harmony" className="mb-2">
                    Meta-Learning
                  </Typography>
                  <Typography variant="sm" color="secondary">
                    The system learns how to learn more effectively
                  </Typography>
                </Card>
              </div>
            </Card>
          </Card>

          {/* SymbioLabs Research Engine */}
          <Card variant="neural" className="mb-16 p-12 border-consciousness/20">
            <Typography as="h2" variant="2xl" weight="bold" color="consciousness" className="mb-8">
              The SymbioLabs Research Engine
            </Typography>
            
            <Typography as="h3" variant="xl" weight="semibold" color="creativity" className="mb-6">
              Nature's Algorithms for Tomorrow's Challenges
            </Typography>
            
            <Typography variant="lg" weight="semibold" color="consciousness" className="mb-6">
              Biomimicry Research Pipeline:
            </Typography>
            
            <div className="grid md:grid-cols-2 gap-8 mb-8">
              <Card variant="consciousness" className="p-6 border-consciousness/25">
                <div className="flex items-start gap-3 mb-4">
                  <div className="w-8 h-8 bg-consciousness/20 rounded-full flex items-center justify-center">
                    <span className="text-consciousness font-bold">1</span>
                  </div>
                  <Typography variant="lg" weight="semibold" color="consciousness">Biological Discovery</Typography>
                </div>
                <div className="space-y-2">
                  <div className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-consciousness mt-1 flex-shrink-0" />
                    <Typography variant="sm" color="secondary">Study natural systems for optimization strategies</Typography>
                  </div>
                  <div className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-consciousness mt-1 flex-shrink-0" />
                    <Typography variant="sm" color="secondary">Identify mathematical principles underlying biological intelligence</Typography>
                  </div>
                  <div className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-consciousness mt-1 flex-shrink-0" />
                    <Typography variant="sm" color="secondary">Validate mechanisms through controlled experiments</Typography>
                  </div>
                </div>
              </Card>

              <Card variant="creativity" className="p-6 border-creativity/25">
                <div className="flex items-start gap-3 mb-4">
                  <div className="w-8 h-8 bg-creativity/20 rounded-full flex items-center justify-center">
                    <span className="text-creativity font-bold">2</span>
                  </div>
                  <Typography variant="lg" weight="semibold" color="creativity">Mathematical Modeling</Typography>
                </div>
                <div className="space-y-2">
                  <div className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-creativity mt-1 flex-shrink-0" />
                    <Typography variant="sm" color="secondary">Translate biological processes into computational frameworks</Typography>
                  </div>
                  <div className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-creativity mt-1 flex-shrink-0" />
                    <Typography variant="sm" color="secondary">Develop rigorous mathematical proofs and stability analysis</Typography>
                  </div>
                  <div className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-creativity mt-1 flex-shrink-0" />
                    <Typography variant="sm" color="secondary">Create scalable algorithmic implementations</Typography>
                  </div>
                </div>
              </Card>

              <Card variant="intuition" className="p-6 border-harmony/25">
                <div className="flex items-start gap-3 mb-4">
                  <div className="w-8 h-8 bg-harmony/20 rounded-full flex items-center justify-center">
                    <span className="text-harmony font-bold">3</span>
                  </div>
                  <Typography variant="lg" weight="semibold" color="harmony">Engineering Implementation</Typography>
                </div>
                <div className="space-y-2">
                  <div className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-harmony mt-1 flex-shrink-0" />
                    <Typography variant="sm" color="secondary">Build production-ready ACI models</Typography>
                  </div>
                  <div className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-harmony mt-1 flex-shrink-0" />
                    <Typography variant="sm" color="secondary">Optimize for specific industrial applications</Typography>
                  </div>
                  <div className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-harmony mt-1 flex-shrink-0" />
                    <Typography variant="sm" color="secondary">Integrate with existing SymbioWave divisions</Typography>
                  </div>
                </div>
              </Card>

              <Card variant="neural" className="p-6 border-consciousness/25">
                <div className="flex items-start gap-3 mb-4">
                  <div className="w-8 h-8 bg-consciousness/20 rounded-full flex items-center justify-center">
                    <span className="text-consciousness font-bold">4</span>
                  </div>
                  <Typography variant="lg" weight="semibold" color="consciousness">Ecosystem Integration</Typography>
                </div>
                <div className="space-y-2">
                  <div className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-consciousness mt-1 flex-shrink-0" />
                    <Typography variant="sm" color="secondary">Deploy new models across the platform</Typography>
                  </div>
                  <div className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-consciousness mt-1 flex-shrink-0" />
                    <Typography variant="sm" color="secondary">Monitor performance and gather feedback</Typography>
                  </div>
                  <div className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-consciousness mt-1 flex-shrink-0" />
                    <Typography variant="sm" color="secondary">Iterate and improve based on real-world results</Typography>
                  </div>
                </div>
              </Card>
            </div>
          </Card>

          {/* Active Research Frontiers */}
          <Card variant="quantum" className="mb-16 p-12 border-consciousness/30">
            <Typography as="h2" variant="2xl" weight="bold" color="consciousness" className="mb-8">
              Active Research Frontiers
            </Typography>
            <Typography variant="lg" color="secondary" className="mb-8">
              Next-Generation ACI Models in Development
            </Typography>
            
            <div className="grid md:grid-cols-2 gap-8">
              <Card variant="consciousness" className="p-8 border-consciousness/25">
                <Microscope className="w-12 h-12 text-consciousness mb-4" />
                <Typography variant="lg" weight="semibold" color="consciousness" className="mb-3">
                  Octopus-Inspired Distributed Problem Solver
                </Typography>
                <div className="space-y-3 mb-4">
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-consciousness rounded-full mt-2 flex-shrink-0"></div>
                    <Typography variant="sm" color="secondary"><strong>Biological Inspiration:</strong> Octopus neural networks with distributed processing</Typography>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-consciousness rounded-full mt-2 flex-shrink-0"></div>
                    <Typography variant="sm" color="secondary"><strong>Application:</strong> Complex multi-objective optimization</Typography>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-consciousness rounded-full mt-2 flex-shrink-0"></div>
                    <Typography variant="sm" color="secondary"><strong>Status:</strong> Alpha testing, 6 months to production</Typography>
                  </div>
                </div>
              </Card>

              <Card variant="creativity" className="p-8 border-creativity/25">
                <Network className="w-12 h-12 text-creativity mb-4" />
                <Typography variant="lg" weight="semibold" color="creativity" className="mb-3">
                  Coral Reef-Inspired Ecosystem Stabilizer
                </Typography>
                <div className="space-y-3 mb-4">
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-creativity rounded-full mt-2 flex-shrink-0"></div>
                    <Typography variant="sm" color="secondary"><strong>Biological Inspiration:</strong> Coral reef resilience and symbiotic relationships</Typography>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-creativity rounded-full mt-2 flex-shrink-0"></div>
                    <Typography variant="sm" color="secondary"><strong>Application:</strong> Supply chain risk management and recovery</Typography>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-creativity rounded-full mt-2 flex-shrink-0"></div>
                    <Typography variant="sm" color="secondary"><strong>Status:</strong> Mathematical modeling phase</Typography>
                  </div>
                </div>
              </Card>
            </div>
          </Card>

          {/* Call to Action */}
          <div className="text-center">
            <Card variant="consciousness" className="p-12 border-consciousness/25">
              <Typography as="h3" variant="2xl" weight="bold" color="consciousness" className="mb-6">
                Join the Innovation Revolution
              </Typography>
              <Typography variant="lg" color="secondary" className="mb-8 max-w-3xl mx-auto">
                Experience how continuous evolution and nature-inspired innovation drive unprecedented AI capabilities.
              </Typography>
              
              <div className="flex flex-col sm:flex-row gap-6 justify-center">
                <Button
                  variant="quantum"
                  size="xl"
                  leftIcon={<Lightbulb className="w-6 h-6" />}
                  rightIcon={<ArrowRight className="w-5 h-5" />}
                  onClick={() => window.location.href = '/symbiolabs'}
                  className="min-w-[200px] font-semibold"
                >
                  Explore SymbioLabs
                </Button>

                <Button
                  variant="professional"
                  size="xl"
                  leftIcon={<Brain className="w-6 h-6" />}
                  onClick={() => window.location.href = '/aci-technology'}
                  className="min-w-[200px] font-semibold"
                >
                  ACI Technology
                </Button>
              </div>
            </Card>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default InnovationEngine;
