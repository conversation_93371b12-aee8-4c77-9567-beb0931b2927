
import React, { useState, useEffect } from 'react';
import { ArrowRight, Zap, Brain, Network, Globe, Cpu, Database, Shield, Monitor, Settings, Code, Activity } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Button from '../components/atoms/Button';
import Card from '../components/atoms/Card';
import AnticipatoryCursor from '../components/atoms/AnticipatoryCursor';
import AuthenticationModal from '../components/auth/AuthenticationModal';
import UserControlPanel from '../components/auth/UserControlPanel';

const Interface: React.FC = () => {
  const { t } = useTranslation();
  const [activeModule, setActiveModule] = useState('overview');
  const [isLoading, setIsLoading] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [user, setUser] = useState<any>(null);

  useEffect(() => {
    // Check if user is already authenticated
    const savedUser = localStorage.getItem('symbiowave_user');
    if (savedUser) {
      setUser(JSON.parse(savedUser));
    } else {
      // Show authentication modal if not logged in
      setShowAuthModal(true);
    }
  }, []);

  const handleAuthSuccess = (userData: any) => {
    setUser(userData);
    setShowAuthModal(false);
  };

  const handleLogout = () => {
    localStorage.removeItem('symbiowave_user');
    setUser(null);
    setShowAuthModal(true);
  };

  const modules = [
    {
      id: 'symbiocore',
      name: 'SymbioCore',
      description: 'Core Intelligence Platform',
      icon: <Brain className="w-6 h-6" />,
      color: 'consciousness' as const,
      status: 'Development'
    },
    {
      id: 'symbioautomate',
      name: 'SymbioAutomate',
      description: 'Intelligent Automation Suite',
      icon: <Zap className="w-6 h-6" />,
      color: 'harmony' as const,
      status: 'Active'
    },
    {
      id: 'symbioxchange',
      name: 'SymbioXchange',
      description: 'Global Resource Exchange',
      icon: <Globe className="w-6 h-6" />,
      color: 'creativity' as const,
      status: 'Development'
    },
    {
      id: 'symbioedge',
      name: 'SymbioEdge',
      description: 'Distributed Edge Intelligence',
      icon: <Network className="w-6 h-6" />,
      color: 'consciousness' as const,
      status: 'Development'
    }
  ];

  const interfaceFeatures = [
    {
      title: 'Unified Dashboard',
      description: 'Monitor all SymbioWave divisions from a single, intelligent interface',
      icon: <Monitor className="w-8 h-8" />
    },
    {
      title: 'ACI Integration',
      description: 'Direct access to Artificial Cellular Intelligence capabilities',
      icon: <Cpu className="w-8 h-8" />
    },
    {
      title: 'Real-time Analytics',
      description: 'Live insights across your entire symbiotic ecosystem',
      icon: <Activity className="w-8 h-8" />
    },
    {
      title: 'Advanced Security',
      description: 'Quantum-encrypted communications and bio-verified access',
      icon: <Shield className="w-8 h-8" />
    }
  ];

  const handleModuleAccess = (moduleId: string) => {
    setIsLoading(true);
    setTimeout(() => {
      if (moduleId === 'symbioautomate') {
        window.location.href = '/symbioautomate';
      } else {
        window.location.href = `/${moduleId}`;
      }
      setIsLoading(false);
    }, 1500);
  };

  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      {/* User Control Panel - Only show if authenticated */}
      {user && (
        <UserControlPanel 
          user={user} 
          onLogout={handleLogout}
        />
      )}
      
      {/* Authentication Modal */}
      <AuthenticationModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        onSuccess={handleAuthSuccess}
      />
      
      <main className={`pt-20 transition-all duration-500 ${user ? 'ml-16' : ''}`}>
        {/* Hero Section */}
        <section className="relative py-20 overflow-hidden">
          {/* Background Effects */}
          <div className="absolute inset-0 opacity-20">
            <div className="absolute top-[30%] left-[20%] w-[400px] h-[400px] bg-consciousness/10 rounded-full blur-3xl animate-pulse"></div>
            <div className="absolute bottom-[20%] right-[30%] w-[300px] h-[300px] bg-creativity/8 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '1s' }}></div>
          </div>

          <div className="container mx-auto px-8 relative z-10">
            <div className="text-center mb-16">
              <Typography
                as="h1"
                variant="4xl"
                weight="bold"
                gradient="consciousness"
                className="mb-6"
              >
                {t('pages.interface.title')}
              </Typography>

              <Typography
                as="p"
                variant="xl"
                color="secondary"
                className="mb-8 max-w-3xl mx-auto"
              >
                {t('pages.interface.subtitle')}
              </Typography>

              <div className="flex flex-col sm:flex-row gap-6 justify-center">
                <Button 
                  variant="quantum" 
                  size="lg"
                  rightIcon={<ArrowRight className="w-5 h-5" />}
                  className="rounded-[20px]"
                  onClick={() => setActiveModule('dashboard')}
                >
                  Launch Interface
                </Button>
                
                <Button 
                  variant="outline-quantum" 
                  size="lg"
                  className="rounded-[20px]"
                  onClick={() => window.location.href = '/about-us'}
                >
                  Learn More
                </Button>
              </div>
            </div>

            {/* Interface Features */}
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
              {interfaceFeatures.map((feature, index) => (
                <Card 
                  key={feature.title}
                  variant="neural" 
                  className="p-6 text-center border-consciousness/20 hover:border-consciousness/40 transition-all duration-500 rounded-[24px]"
                >
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-consciousness/10 flex items-center justify-center border border-consciousness/30">
                    {feature.icon}
                  </div>
                  <Typography variant="lg" weight="semibold" color="consciousness" className="mb-2">
                    {feature.title}
                  </Typography>
                  <Typography variant="sm" color="tertiary">
                    {feature.description}
                  </Typography>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Module Access Grid */}
        <section className="py-20 relative">
          <div className="container mx-auto px-8">
            <div className="text-center mb-16">
              <Typography 
                as="h2" 
                variant="3xl" 
                weight="bold" 
                color="consciousness"
                className="mb-4"
              >
                Access Neural Divisions
              </Typography>
              <Typography 
                variant="lg" 
                color="secondary"
                className="max-w-2xl mx-auto"
              >
                Each division represents a specialized aspect of the SymbioWave ecosystem, 
                working in harmony to create unprecedented AI capabilities.
              </Typography>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {modules.map((module) => (
                <AnticipatoryCursor key={module.id} intensity="medium" glowColor={module.color}>
                  <Card 
                    variant="quantum" 
                    className="p-8 group cursor-pointer hover:scale-105 transition-all duration-500 border-consciousness/30 hover:border-consciousness/60 rounded-[24px]"
                    onClick={() => handleModuleAccess(module.id)}
                  >
                    <div className="text-center">
                      <div className={`w-16 h-16 mx-auto mb-6 rounded-full bg-${module.color}/15 flex items-center justify-center border border-${module.color}/30 group-hover:scale-110 transition-transform duration-300`}>
                        {module.icon}
                      </div>
                      
                      <Typography variant="lg" weight="bold" color={module.color} className="mb-2">
                        {module.name}
                      </Typography>
                      
                      <Typography variant="sm" color="secondary" className="mb-4">
                        {module.description}
                      </Typography>
                      
                      <div className="flex items-center justify-center gap-2">
                        <div className={`w-2 h-2 rounded-full ${module.status === 'Active' ? 'bg-harmony' : 'bg-consciousness'} animate-pulse`}></div>
                        <Typography variant="xs" color="tertiary">
                          {module.status}
                        </Typography>
                      </div>
                    </div>
                  </Card>
                </AnticipatoryCursor>
              ))}
            </div>
          </div>
        </section>

        {/* System Status */}
        <section className="py-20 bg-abyssal-base/50">
          <div className="container mx-auto px-8">
            <div className="text-center mb-12">
              <Typography 
                as="h2" 
                variant="2xl" 
                weight="bold" 
                color="consciousness"
                className="mb-4"
              >
                System Status
              </Typography>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              <Card variant="neural" className="p-6 border-harmony/20 rounded-[20px]">
                <div className="flex items-center justify-between mb-4">
                  <Typography variant="sm" weight="semibold" color="harmony">
                    ACI Core Processing
                  </Typography>
                  <div className="w-3 h-3 rounded-full bg-harmony animate-pulse"></div>
                </div>
                <Typography variant="2xl" weight="bold" color="harmony" className="mb-2">
                  98.7%
                </Typography>
                <Typography variant="xs" color="tertiary">
                  Optimal Performance
                </Typography>
              </Card>

              <Card variant="neural" className="p-6 border-consciousness/20 rounded-[20px]">
                <div className="flex items-center justify-between mb-4">
                  <Typography variant="sm" weight="semibold" color="consciousness">
                    Network Synchronization
                  </Typography>
                  <div className="w-3 h-3 rounded-full bg-consciousness animate-pulse"></div>
                </div>
                <Typography variant="2xl" weight="bold" color="consciousness" className="mb-2">
                  99.2%
                </Typography>
                <Typography variant="xs" color="tertiary">
                  All Systems Online
                </Typography>
              </Card>

              <Card variant="neural" className="p-6 border-creativity/20 rounded-[20px]">
                <div className="flex items-center justify-between mb-4">
                  <Typography variant="sm" weight="semibold" color="creativity">
                    Active Workflows
                  </Typography>
                  <div className="w-3 h-3 rounded-full bg-creativity animate-pulse"></div>
                </div>
                <Typography variant="2xl" weight="bold" color="creativity" className="mb-2">
                  1,247
                </Typography>
                <Typography variant="xs" color="tertiary">
                  Currently Processing
                </Typography>
              </Card>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="py-20">
          <div className="container mx-auto px-8 text-center">
            <Typography 
              as="h2" 
              variant="3xl" 
              weight="bold" 
              gradient="consciousness"
              className="mb-6"
            >
              Ready to Transform Your Business?
            </Typography>
            
            <Typography 
              variant="lg" 
              color="secondary"
              className="mb-8 max-w-2xl mx-auto"
            >
              Experience the power of symbiotic intelligence. Start with SymbioAutomate 
              and discover how ACI can revolutionize your operations.
            </Typography>

            <Button 
              variant="quantum" 
              size="lg"
              rightIcon={<ArrowRight className="w-5 h-5" />}
              className="rounded-[20px]"
              onClick={() => window.location.href = '/symbioautomate'}
            >
              Begin Your Journey
            </Button>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default Interface;
