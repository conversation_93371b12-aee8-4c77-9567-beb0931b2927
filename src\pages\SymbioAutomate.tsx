import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  <PERSON>R<PERSON>, 
  Zap, 
  Brain, 
  TrendingUp, 
  Shield, 
  Clock, 
  Target, 
  CheckCircle,
  Play,
  BarChart3,
  Settings,
  Workflow,
  Database,
  Users,
  Award,
  Lightbulb,
  Rocket,
  Atom,
  Orbit,
  Network,
  Cpu,
  Eye,
  Layers,
  GitBranch,
  Activity,
  Sparkles,
  Hexagon,
  Triangle,
  Circle
} from 'lucide-react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Button from '../components/atoms/Button';
import Card from '../components/atoms/Card';

const SymbioAutomate: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('overview');
  const [isVisible, setIsVisible] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [activeFeature, setActiveFeature] = useState<number | null>(null);
  const [animationPhase, setAnimationPhase] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const heroRef = useRef<HTMLElement>(null);

  useEffect(() => {
    setIsVisible(true);
    
    // Animation phase cycling
    const interval = setInterval(() => {
      setAnimationPhase(prev => (prev + 1) % 4);
    }, 3000);
    
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (heroRef.current) {
        const rect = heroRef.current.getBoundingClientRect();
        setMousePosition({
          x: (e.clientX - rect.left) / rect.width,
          y: (e.clientY - rect.top) / rect.height,
        });
      }
    };

    const hero = heroRef.current;
    if (hero) {
      hero.addEventListener('mousemove', handleMouseMove);
      return () => hero.removeEventListener('mousemove', handleMouseMove);
    }
  }, []);

  const automationBenefits = [
    {
      icon: <Brain className="w-8 h-8" />,
      title: t('pages.symbioAutomate.benefits.intelligentProcessing.title'),
      metric: t('pages.symbioAutomate.benefits.intelligentProcessing.metric'),
      description: t('pages.symbioAutomate.benefits.intelligentProcessing.description'),
      color: "blue-500",
      particles: 12
    },
    {
      icon: <Zap className="w-8 h-8" />,
      title: t('pages.symbioAutomate.benefits.lightningSpeed.title'),
      metric: t('pages.symbioAutomate.benefits.lightningSpeed.metric'),
      description: t('pages.symbioAutomate.benefits.lightningSpeed.description'),
      color: "purple-500",
      particles: 8
    },
    {
      icon: <Network className="w-8 h-8" />,
      title: t('pages.symbioAutomate.benefits.smartIntegration.title'),
      metric: t('pages.symbioAutomate.benefits.smartIntegration.metric'),
      description: t('pages.symbioAutomate.benefits.smartIntegration.description'),
      color: "green-500",
      particles: 15
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: "Enterprise Security",
      metric: "Bank-Level Protection",
      description: "Advanced security protocols to keep your data safe and compliant",
      color: "blue-500",
      particles: 10
    }
  ];

  const businessUseCases = [
    {
      title: t('pages.symbioAutomate.useCases.customerService.title'),
      description: t('pages.symbioAutomate.useCases.customerService.description'),
      roi: t('pages.symbioAutomate.useCases.customerService.roi'),
      industry: t('pages.symbioAutomate.useCases.customerService.industry'),
      icon: <Users className="w-6 h-6" />,
      complexity: t('pages.symbioAutomate.useCases.customerService.complexity'),
      timeline: t('pages.symbioAutomate.useCases.customerService.timeline'),
      impact: t('pages.symbioAutomate.useCases.customerService.impact')
    },
    {
      title: t('pages.symbioAutomate.useCases.dataProcessing.title'),
      description: t('pages.symbioAutomate.useCases.dataProcessing.description'),
      roi: t('pages.symbioAutomate.useCases.dataProcessing.roi'),
      industry: t('pages.symbioAutomate.useCases.dataProcessing.industry'),
      icon: <BarChart3 className="w-6 h-6" />,
      complexity: t('pages.symbioAutomate.useCases.dataProcessing.complexity'),
      timeline: t('pages.symbioAutomate.useCases.dataProcessing.timeline'),
      impact: t('pages.symbioAutomate.useCases.dataProcessing.impact')
    },
    {
      title: t('pages.symbioAutomate.useCases.workflowOptimization.title'),
      description: t('pages.symbioAutomate.useCases.workflowOptimization.description'),
      roi: t('pages.symbioAutomate.useCases.workflowOptimization.roi'),
      industry: t('pages.symbioAutomate.useCases.workflowOptimization.industry'),
      icon: <Workflow className="w-6 h-6" />,
      complexity: t('pages.symbioAutomate.useCases.workflowOptimization.complexity'),
      timeline: t('pages.symbioAutomate.useCases.workflowOptimization.timeline'),
      impact: t('pages.symbioAutomate.useCases.workflowOptimization.impact')
    },
    {
      title: t('pages.symbioAutomate.useCases.supplyChain.title'),
      description: t('pages.symbioAutomate.useCases.supplyChain.description'),
      roi: t('pages.symbioAutomate.useCases.supplyChain.roi'),
      industry: t('pages.symbioAutomate.useCases.supplyChain.industry'),
      icon: <Layers className="w-6 h-6" />,
      complexity: t('pages.symbioAutomate.useCases.supplyChain.complexity'),
      timeline: t('pages.symbioAutomate.useCases.supplyChain.timeline'),
      impact: t('pages.symbioAutomate.useCases.supplyChain.impact')
    }
  ];

  const platformFeatures = [
    {
      title: "AI-Powered Automation",
      description: "Intelligent automation that learns from your business patterns and optimizes workflows",
      icon: <Brain className="w-8 h-8" />,
      capabilities: [
        "Machine learning optimization",
        "Pattern recognition and analysis",
        "Predictive workflow suggestions",
        "Continuous improvement algorithms"
      ],
      techLevel: "Advanced AI",
      category: "Intelligence"
    },
    {
      title: "Visual Workflow Builder",
      description: "Drag-and-drop interface for creating complex automation workflows without coding",
      icon: <Settings className="w-8 h-8" />,
      capabilities: [
        "No-code workflow design",
        "Pre-built automation templates",
        "Real-time workflow testing",
        "Version control and rollback"
      ],
      techLevel: "User-Friendly",
      category: "Design"
    },
    {
      title: "Real-Time Analytics",
      description: "Comprehensive monitoring and analytics for all your automated processes",
      icon: <BarChart3 className="w-8 h-8" />,
      capabilities: [
        "Performance monitoring dashboards",
        "Custom reporting and alerts",
        "ROI tracking and analysis",
        "Bottleneck identification"
      ],
      techLevel: "Enterprise",
      category: "Analytics"
    },
    {
      title: "Enterprise Integration",
      description: "Seamless connectivity with your existing business systems and third-party tools",
      icon: <Database className="w-8 h-8" />,
      capabilities: [
        "API-first architecture",
        "Pre-built integrations library",
        "Custom connector development",
        "Data synchronization protocols"
      ],
      techLevel: "Enterprise",
      category: "Integration"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-900 text-white overflow-hidden">
      <Header />
      
      <main className="pt-20">
        {/* Hero Section */}
        <section 
          ref={heroRef}
          className="relative py-32 overflow-hidden min-h-screen flex items-center bg-gradient-to-br from-gray-900 via-blue-900/20 to-purple-900/20"
        >
          {/* Background Effects */}
          <div className="absolute inset-0 opacity-30">
            {/* Animated Background Layers */}
            <div className="absolute inset-0">
              {[...Array(20)].map((_, i) => (
                <div
                  key={i}
                  className="absolute rounded-full animate-pulse"
                  style={{
                    width: `${50 + i * 30}px`,
                    height: `${50 + i * 30}px`,
                    background: `radial-gradient(circle, ${
                      i % 3 === 0 ? 'rgba(59, 130, 246, 0.1)' :
                      i % 3 === 1 ? 'rgba(147, 51, 234, 0.1)' :
                      'rgba(236, 72, 153, 0.1)'
                    }, transparent)`,
                    top: `${Math.sin(i * 0.5) * 40 + 30}%`,
                    left: `${Math.cos(i * 0.3) * 40 + 30}%`,
                    animationDelay: `${i * 0.2}s`,
                    animationDuration: `${3 + i * 0.1}s`
                  }}
                />
              ))}
            </div>

            {/* Network Connections */}
            <svg className="absolute inset-0 w-full h-full">
              <defs>
                <linearGradient id="networkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="rgb(59, 130, 246)" stopOpacity="0.3" />
                  <stop offset="50%" stopColor="rgb(147, 51, 234)" stopOpacity="0.5" />
                  <stop offset="100%" stopColor="rgb(236, 72, 153)" stopOpacity="0.3" />
                </linearGradient>
              </defs>
              
              {[...Array(15)].map((_, i) => (
                <line
                  key={i}
                  x1={`${Math.random() * 100}%`}
                  y1={`${Math.random() * 100}%`}
                  x2={`${Math.random() * 100}%`}
                  y2={`${Math.random() * 100}%`}
                  stroke="url(#networkGradient)"
                  strokeWidth="1"
                  opacity="0.4"
                  className="animate-pulse"
                  style={{ animationDelay: `${i * 0.3}s` }}
                />
              ))}
            </svg>

            {/* Particle System */}
            <div className="absolute inset-0">
              {[...Array(50)].map((_, i) => (
                <div
                  key={i}
                  className="absolute w-1 h-1 bg-blue-400 rounded-full animate-ping opacity-60"
                  style={{
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`,
                    animationDelay: `${Math.random() * 3}s`,
                    animationDuration: `${2 + Math.random() * 2}s`
                  }}
                />
              ))}
            </div>
          </div>

          <div className="container mx-auto px-8 relative z-10">
            <div className="max-w-7xl mx-auto">
              <div className="text-center mb-20">
                {/* Status Indicator */}
                <div className="inline-flex items-center space-x-4 px-8 py-4 bg-blue-500/10 border border-blue-400/30 rounded-full mb-12 backdrop-blur-xl">
                  <div className="relative">
                    <Zap className="w-6 h-6 text-blue-400 animate-pulse" />
                    <div className="absolute inset-0 bg-blue-400/20 rounded-full blur-sm animate-pulse" />
                  </div>
                  <Typography variant="sm" weight="semibold" className="font-mono tracking-wider text-blue-400">
                    AUTOMATION PLATFORM - ACTIVE
                  </Typography>
                  <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse" />
                </div>

                {/* Title */}
                <div className="relative mb-12">
                  <Typography 
                    variant="cosmic" 
                    weight="bold" 
                    className="mb-4 leading-tight bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent font-mono relative"
                  >
                    <span className="relative inline-block">
                      SymbioAutomate
                      {/* Scan Lines */}
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-400/20 to-transparent transform -skew-x-12 animate-pulse opacity-50" />
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-purple-400/20 to-transparent transform skew-x-12 animate-pulse opacity-30" style={{ animationDelay: '1s' }} />
                    </span>
                  </Typography>
                  
                  {/* Indicator */}
                  <div className="absolute -top-4 -right-4 w-8 h-8 border border-blue-400/40 rounded-full animate-spin" style={{ animationDuration: '8s' }}>
                    <div className="absolute inset-1 border border-purple-400/30 rounded-full animate-spin" style={{ animationDuration: '6s', animationDirection: 'reverse' }} />
                  </div>
                </div>

                <Typography 
                  variant="2xl" 
                  weight="semibold" 
                  className="mb-8 text-white"
                >
                  Intelligent Business Automation Platform
                </Typography>

                <Typography 
                  variant="xl" 
                  className="mb-16 max-w-5xl mx-auto leading-relaxed font-light text-gray-300"
                >
                  Transform your business operations with AI-powered automation that streamlines workflows, 
                  reduces manual tasks, and accelerates growth. Experience the future of intelligent business processes.
                </Typography>

                {/* Action Buttons */}
                <div className="flex flex-col lg:flex-row gap-8 justify-center mb-20">
                  <Button 
                    variant="quantum" 
                    size="lg"
                    rightIcon={<Rocket className="w-6 h-6" />}
                    className="group relative overflow-hidden rounded-[24px] px-12 py-6 text-lg font-semibold bg-blue-600 hover:bg-blue-700 border-2 border-blue-500/40 hover:scale-110 transition-all duration-700"
                    onClick={() => window.location.href = '/request-demo'}
                  >
                    <span className="relative z-10 font-mono tracking-wide">START FREE TRIAL</span>
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-600/30 to-purple-600/30 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-700 origin-left" />
                    <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700">
                      {[...Array(6)].map((_, i) => (
                        <div
                          key={i}
                          className="absolute w-1 h-1 bg-white rounded-full animate-ping"
                          style={{
                            top: `${20 + i * 15}%`,
                            left: `${10 + i * 15}%`,
                            animationDelay: `${i * 0.1}s`
                          }}
                        />
                      ))}
                    </div>
                  </Button>
                  
                  <Button 
                    variant="outline-quantum" 
                    size="lg"
                    leftIcon={<Play className="w-6 h-6" />}
                    className="group rounded-[24px] px-12 py-6 text-lg font-semibold border-2 border-purple-400/40 text-white hover:text-purple-400 hover:scale-110 transition-all duration-700"
                    onClick={() => window.location.href = '/ai-readiness-audit'}
                  >
                    <span className="font-mono tracking-wide group-hover:text-purple-400 transition-colors duration-500">{t('pages.symbioAutomate.buttons.watchDemo')}</span>
                  </Button>
                </div>

                {/* Benefits Grid */}
                <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                  {automationBenefits.map((benefit, index) => (
                    <div
                      key={benefit.title}
                      onMouseEnter={() => setActiveFeature(index)}
                      onMouseLeave={() => setActiveFeature(null)}
                    >
                      <Card 
                        variant="neural" 
                        className={`relative p-8 text-center border-2 border-gray-700 hover:border-${benefit.color} transition-all duration-700 rounded-[24px] group hover:scale-110 backdrop-blur-xl overflow-hidden bg-gray-800/50`}
                      >
                        {/* Particle Field */}
                        <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-all duration-1000">
                          {[...Array(benefit.particles)].map((_, i) => (
                            <div
                              key={i}
                              className={`absolute w-1 h-1 bg-${benefit.color} rounded-full animate-ping`}
                              style={{
                                top: `${Math.random() * 100}%`,
                                left: `${Math.random() * 100}%`,
                                animationDelay: `${i * 0.1}s`,
                                animationDuration: '2s'
                              }}
                            />
                          ))}
                        </div>

                        {/* Icon */}
                        <div className={`relative w-20 h-20 mx-auto mb-6 rounded-full bg-${benefit.color}/10 flex items-center justify-center border-2 border-${benefit.color}/40 group-hover:scale-125 transition-all duration-500`}>
                          <div className={`absolute inset-0 bg-${benefit.color}/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500`} />
                          <div className={`text-${benefit.color} relative z-10 group-hover:scale-110 transition-transform duration-300`}>
                            {benefit.icon}
                          </div>
                        </div>

                        <Typography variant="lg" weight="bold" className={`mb-2 font-mono text-${benefit.color}`}>
                          {benefit.title}
                        </Typography>
                        
                        <Typography variant="sm" weight="semibold" className={`mb-3 text-${benefit.color}/80 font-mono tracking-wider`}>
                          {benefit.metric}
                        </Typography>
                        
                        <Typography variant="xs" className="leading-relaxed text-gray-300">
                          {benefit.description}
                        </Typography>
                      </Card>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Gradient */}
          <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-gray-900/80 to-transparent" />
        </section>

        {/* Use Cases Section */}
        <section className="py-32 bg-gradient-to-br from-gray-900 via-blue-900/10 to-purple-900/10 relative overflow-hidden">
          {/* Background Grid */}
          <div className="absolute inset-0 opacity-10">
            <div className="grid grid-cols-12 grid-rows-12 h-full w-full">
              {[...Array(144)].map((_, i) => (
                <div
                  key={i}
                  className="border border-blue-400/20 animate-pulse"
                  style={{ animationDelay: `${i * 0.01}s` }}
                />
              ))}
            </div>
          </div>

          <div className="container mx-auto px-8 relative z-10">
            <div className="text-center mb-20">
              <div className="flex items-center justify-center mb-8">
                <Hexagon className="w-8 h-8 text-blue-400 mr-4 animate-spin" style={{ animationDuration: '6s' }} />
                <Typography variant="3xl" weight="bold" className="font-mono bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent">
                  {t('pages.symbioAutomate.useCases.title')}
                </Typography>
                <Triangle className="w-8 h-8 text-purple-400 ml-4 animate-pulse" />
              </div>
              <Typography variant="lg" className="max-w-4xl mx-auto font-light text-gray-300">
                {t('pages.symbioAutomate.useCases.subtitle')}
              </Typography>
            </div>

            <div className="grid lg:grid-cols-2 gap-12 max-w-7xl mx-auto">
              {businessUseCases.map((useCase, index) => (
                <Card 
                  key={useCase.title} 
                  variant="neural" 
                  className="relative p-10 border-2 border-gray-700 hover:border-blue-400/50 transition-all duration-700 rounded-[32px] group backdrop-blur-xl overflow-hidden bg-gray-800/50"
                >
                  {/* Background Effect */}
                  <div className="absolute inset-0 opacity-0 group-hover:opacity-20 transition-all duration-1000">
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-pink-500/10 animate-pulse" />
                  </div>

                  <div className="relative z-10">
                    <div className="flex items-start justify-between mb-8">
                      <div className="flex items-center space-x-6">
                        <div className="w-16 h-16 rounded-full bg-blue-500/10 flex items-center justify-center border-2 border-blue-400/40 group-hover:scale-125 transition-all duration-500 relative">
                          <div className="absolute inset-0 bg-blue-500/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                          <div className="text-blue-400 relative z-10">
                            {useCase.icon}
                          </div>
                        </div>
                        <div>
                          <Typography variant="xl" weight="bold" className="mb-2 font-mono text-white">
                            {useCase.title}
                          </Typography>
                          <div className="flex items-center space-x-4">
                            <span className="px-3 py-1 bg-green-500/20 text-green-400 text-xs rounded-full border border-green-400/40 font-mono">
                              {useCase.industry}
                            </span>
                            <span className="px-3 py-1 bg-purple-500/20 text-purple-400 text-xs rounded-full border border-purple-400/40 font-mono">
                              {useCase.complexity}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <Typography variant="sm" className="mb-8 leading-relaxed text-gray-300">
                      {useCase.description}
                    </Typography>

                    <div className="grid grid-cols-3 gap-6 mb-6">
                      <div className="text-center">
                        <Typography variant="xs" className="mb-1 font-mono text-gray-400">IMPACT</Typography>
                        <Typography variant="sm" weight="semibold" className="font-mono text-green-400">
                          {useCase.impact}
                        </Typography>
                      </div>
                      <div className="text-center">
                        <Typography variant="xs" className="mb-1 font-mono text-gray-400">TIMELINE</Typography>
                        <Typography variant="sm" weight="semibold" className="font-mono text-purple-400">
                          {useCase.timeline}
                        </Typography>
                      </div>
                      <div className="text-center">
                        <Typography variant="xs" className="mb-1 font-mono text-gray-400">ROI</Typography>
                        <Typography variant="sm" weight="semibold" className="font-mono text-blue-400">
                          {useCase.roi}
                        </Typography>
                      </div>
                    </div>

                    {/* Progress Indicator */}
                    <div className="w-full h-1 bg-gray-700 rounded-full overflow-hidden">
                      <div className="h-full bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 animate-pulse" style={{ width: '100%' }} />
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            <div className="text-center mt-16">
              <Button 
                variant="outline-quantum" 
                size="lg"
                rightIcon={<ArrowRight className="w-5 h-5" />}
                className="rounded-[24px] border-2 border-blue-400/40 hover:scale-110 transition-all duration-500 font-mono text-white hover:text-blue-400"
                onClick={() => window.location.href = '/use-cases'}
              >
                EXPLORE ALL USE CASES
              </Button>
            </div>
          </div>
        </section>

        {/* Platform Features Section */}
        <section className="py-32 relative overflow-hidden bg-gray-900">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-20">
            <svg className="w-full h-full">
              <defs>
                <pattern id="featurePattern" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
                  <circle cx="50" cy="50" r="2" fill="rgb(59, 130, 246)" opacity="0.3" />
                  <line x1="0" y1="50" x2="100" y2="50" stroke="rgb(147, 51, 234)" strokeWidth="0.5" opacity="0.2" />
                  <line x1="50" y1="0" x2="50" y2="100" stroke="rgb(236, 72, 153)" strokeWidth="0.5" opacity="0.2" />
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#featurePattern)" />
            </svg>
          </div>

          <div className="container mx-auto px-8 relative z-10">
            <div className="text-center mb-20">
              <div className="flex items-center justify-center mb-8">
                <Circle className="w-8 h-8 text-blue-400 mr-4 animate-pulse" />
                <Typography variant="3xl" weight="bold" className="font-mono bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent">
                  Platform Features
                </Typography>
                <Settings className="w-8 h-8 text-purple-400 ml-4 animate-spin" style={{ animationDuration: '4s' }} />
              </div>
              <Typography variant="lg" className="max-w-4xl mx-auto font-light text-gray-300">
                Powerful features designed to streamline your business operations and drive growth
              </Typography>
            </div>

            <div className="grid lg:grid-cols-2 gap-16 max-w-7xl mx-auto">
              {platformFeatures.map((feature, index) => (
                <div
                  key={feature.title}
                  onMouseEnter={() => setActiveFeature(index)}
                  onMouseLeave={() => setActiveFeature(null)}
                >
                  <Card 
                    variant="neural" 
                    className="relative p-12 border-2 border-gray-700 rounded-[32px] group hover:border-blue-400/50 transition-all duration-700 backdrop-blur-xl overflow-hidden bg-gray-800/50"
                  >
                    {/* Background Effect */}
                    <div className="absolute inset-0 opacity-0 group-hover:opacity-30 transition-all duration-1000">
                      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-pink-500/10 animate-pulse" />
                      {[...Array(20)].map((_, i) => (
                        <div
                          key={i}
                          className="absolute w-1 h-1 bg-blue-400 rounded-full animate-ping"
                          style={{
                            top: `${Math.random() * 100}%`,
                            left: `${Math.random() * 100}%`,
                            animationDelay: `${i * 0.1}s`,
                            animationDuration: '3s'
                          }}
                        />
                      ))}
                    </div>

                    <div className="relative z-10">
                      <div className="flex items-start space-x-8 mb-8">
                        <div className="w-20 h-20 rounded-full bg-blue-500/10 flex items-center justify-center border-2 border-blue-400/40 flex-shrink-0 group-hover:scale-125 transition-all duration-500 relative">
                          <div className="absolute inset-0 bg-blue-500/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                          <div className="text-blue-400 relative z-10">
                            {feature.icon}
                          </div>
                        </div>
                        
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-4">
                            <Typography variant="xl" weight="bold" className="font-mono text-white">
                              {feature.title}
                            </Typography>
                            <div className="flex items-center space-x-2">
                              <span className="px-3 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full border border-blue-400/40 font-mono">
                                {feature.techLevel}
                              </span>
                              <span className="px-3 py-1 bg-purple-500/20 text-purple-400 text-xs rounded-full border border-purple-400/40 font-mono">
                                {feature.category}
                              </span>
                            </div>
                          </div>
                          <Typography variant="sm" className="mb-8 leading-relaxed text-gray-300">
                            {feature.description}
                          </Typography>
                        </div>
                      </div>

                      <div className="space-y-4">
                        {feature.capabilities.map((capability, capIndex) => (
                          <div key={capIndex} className="flex items-center space-x-4 group/item">
                            <div className="w-6 h-6 rounded-full bg-green-500/20 flex items-center justify-center border border-green-400/40 flex-shrink-0 group-hover/item:scale-110 transition-transform duration-300">
                              <CheckCircle className="w-3 h-3 text-green-400" />
                            </div>
                            <Typography variant="sm" className="font-mono tracking-wide text-gray-300">
                              {capability}
                            </Typography>
                          </div>
                        ))}
                      </div>

                      {/* Status */}
                      <div className="mt-8 pt-6 border-t border-gray-700">
                        <div className="flex items-center justify-between">
                          <Typography variant="xs" className="font-mono text-gray-400">
                            FEATURE STATUS
                          </Typography>
                          <div className="flex items-center space-x-2">
                            <Activity className="w-4 h-4 text-green-400 animate-pulse" />
                            <Typography variant="xs" className="font-mono text-green-400">
                              ACTIVE
                            </Typography>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-32 bg-gradient-to-br from-blue-900/20 via-gray-900 to-purple-900/20 relative overflow-hidden">
          {/* Particle Field */}
          <div className="absolute inset-0 opacity-40">
            {[...Array(100)].map((_, i) => (
              <div
                key={i}
                className="absolute w-1 h-1 bg-blue-400 rounded-full animate-ping"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  animationDelay: `${Math.random() * 5}s`,
                  animationDuration: `${2 + Math.random() * 3}s`
                }}
              />
            ))}
          </div>

          <div className="container mx-auto px-8 text-center relative z-10">
            <div className="max-w-6xl mx-auto">
              <div className="mb-16">
                <Typography variant="3xl" weight="bold" className="mb-8 font-mono bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent">
                  Ready to Transform Your Business?
                </Typography>
                
                <Typography variant="xl" className="mb-16 font-light text-gray-300">
                  Join thousands of businesses already using SymbioAutomate to streamline operations, 
                  reduce costs, and accelerate growth with intelligent automation.
                </Typography>
              </div>

              <div className="grid md:grid-cols-3 gap-12 mb-16">
                <Card variant="neural" className="p-8 border-2 border-green-400/30 text-center backdrop-blur-xl rounded-[24px] group hover:scale-105 transition-all duration-500 bg-gray-800/50">
                  <div className="w-20 h-20 mx-auto mb-6 rounded-full bg-green-500/10 flex items-center justify-center border-2 border-green-400/40 group-hover:scale-110 transition-transform duration-300">
                    <Users className="w-10 h-10 text-green-400" />
                  </div>
                  <Typography variant="lg" weight="bold" className="mb-4 font-mono text-green-400">
                    Expert Consultation
                  </Typography>
                  <Typography variant="sm" className="leading-relaxed text-gray-300">
                    Get personalized guidance from our automation specialists to design the perfect solution
                  </Typography>
                </Card>

                <Card variant="neural" className="p-8 border-2 border-blue-400/30 text-center backdrop-blur-xl rounded-[24px] group hover:scale-105 transition-all duration-500 bg-gray-800/50">
                  <div className="w-20 h-20 mx-auto mb-6 rounded-full bg-blue-500/10 flex items-center justify-center border-2 border-blue-400/40 group-hover:scale-110 transition-transform duration-300">
                    <Rocket className="w-10 h-10 text-blue-400" />
                  </div>
                  <Typography variant="lg" weight="bold" className="mb-4 font-mono text-blue-400">
                    Rapid Implementation
                  </Typography>
                  <Typography variant="sm" className="leading-relaxed text-gray-300">
                    Quick deployment with minimal disruption to your existing business operations
                  </Typography>
                </Card>

                <Card variant="neural" className="p-8 border-2 border-purple-400/30 text-center backdrop-blur-xl rounded-[24px] group hover:scale-105 transition-all duration-500 bg-gray-800/50">
                  <div className="w-20 h-20 mx-auto mb-6 rounded-full bg-purple-500/10 flex items-center justify-center border-2 border-purple-400/40 group-hover:scale-110 transition-transform duration-300">
                    <TrendingUp className="w-10 h-10 text-purple-400" />
                  </div>
                  <Typography variant="lg" weight="bold" className="mb-4 font-mono text-purple-400">
                    Measurable Results
                  </Typography>
                  <Typography variant="sm" className="leading-relaxed text-gray-300">
                    Track ROI and performance improvements with comprehensive analytics and reporting
                  </Typography>
                </Card>
              </div>

              <div className="flex flex-col lg:flex-row gap-8 justify-center mb-12">
                <Button 
                  variant="quantum" 
                  size="lg"
                  rightIcon={<Sparkles className="w-6 h-6" />}
                  className="group relative overflow-hidden rounded-[24px] px-16 py-6 text-lg font-semibold bg-blue-600 hover:bg-blue-700 border-2 border-blue-500/40 hover:scale-110 transition-all duration-700"
                  onClick={() => window.location.href = '/request-demo'}
                >
                  <span className="relative z-10 font-mono tracking-wide">START FREE TRIAL</span>
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-600/30 to-purple-600/30 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-700 origin-left" />
                </Button>
                
                <Button 
                  variant="outline-quantum" 
                  size="lg"
                  leftIcon={<Play className="w-6 h-6" />}
                  className="rounded-[24px] px-16 py-6 text-lg font-semibold border-2 border-purple-400/40 text-white hover:text-purple-400 hover:scale-110 transition-all duration-700"
                  onClick={() => window.location.href = '/ai-readiness-audit'}
                >
                  <span className="font-mono tracking-wide">{t('pages.symbioAutomate.buttons.scheduleDemo')}</span>
                </Button>
              </div>

              <div className="grid md:grid-cols-2 gap-8 mb-12">
                <Button 
                  variant="outline-quantum" 
                  size="lg"
                  rightIcon={<Workflow className="w-5 h-5" />}
                  className="rounded-[20px] border border-green-400/40 hover:scale-105 transition-all duration-500 text-white hover:text-green-400"
                  onClick={() => window.location.href = '/workflow-automation'}
                >
                  <span className="font-mono">{t('pages.symbioAutomate.buttons.workflowAutomation')}</span>
                </Button>

                <Button
                  variant="outline-quantum"
                  size="lg"
                  rightIcon={<BarChart3 className="w-5 h-5" />}
                  className="rounded-[20px] border border-purple-400/40 hover:scale-105 transition-all duration-500 text-white hover:text-purple-400"
                  onClick={() => window.location.href = '/ai-transformation'}
                >
                  <span className="font-mono">{t('pages.symbioAutomate.buttons.aiTransformation')}</span>
                </Button>
              </div>

              <div className="pt-12 border-t border-gray-700">
                <Typography variant="sm" className="mb-6 font-mono tracking-wider text-gray-400">
                  {t('pages.symbioAutomate.cta.ready')}
                </Typography>
                <Button
                  variant="outline-quantum"
                  size="lg"
                  rightIcon={<ArrowRight className="w-5 h-5" />}
                  className="rounded-[20px] hover:scale-105 transition-all duration-500 text-white hover:text-blue-400"
                  onClick={() => window.location.href = '/documentation'}
                >
                  <span className="font-mono">{t('pages.symbioAutomate.buttons.viewDocumentation')}</span>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default SymbioAutomate;
