
import React from 'react';
import { useTranslation } from 'react-i18next';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Card from '../components/atoms/Card';
import Button from '../components/atoms/Button';
import { Eye, Lightbulb, Globe, Zap, ArrowRight, Users, Target } from 'lucide-react';

const Vision: React.FC = () => {
  const { t } = useTranslation();

  const visionPillars = [
    {
      icon: Eye,
      title: t('pages.vision.pillars.symbioticIntelligence.title'),
      description: t('pages.vision.pillars.symbioticIntelligence.description'),
      timeframe: t('pages.vision.pillars.symbioticIntelligence.timeframe')
    },
    {
      icon: Globe,
      title: t('pages.vision.pillars.globalAccessibility.title'),
      description: t('pages.vision.pillars.globalAccessibility.description'),
      timeframe: t('pages.vision.pillars.globalAccessibility.timeframe')
    },
    {
      icon: Lightbulb,
      title: t('pages.vision.pillars.emergentSolutions.title'),
      description: t('pages.vision.pillars.emergentSolutions.description'),
      timeframe: t('pages.vision.pillars.emergentSolutions.timeframe')
    }
  ];

  const futureScenarios = [
    {
      title: "Healthcare Revolution",
      description: "ACI systems discover personalized treatments by analyzing patterns across millions of cellular interactions.",
      impact: "Personalized medicine for all"
    },
    {
      title: "Climate Optimization",
      description: "Symbiotic networks coordinate global resources in real-time, optimizing for sustainability and efficiency.",
      impact: "Carbon neutrality by 2030"
    },
    {
      title: "Economic Symbiosis",
      description: "Businesses operate as living ecosystems, where value flows naturally to where it's most needed.",
      impact: "Post-scarcity economics"
    }
  ];

  const milestones = [
    { year: "2025", achievement: "SymbioCore reaches 1M+ active ACI models" },
    { year: "2026", achievement: "SymbioAutomate automates 50% of business processes" },
    { year: "2027", achievement: "SymbioXchange enables $100B+ in optimized resource allocation" },
    { year: "2028", achievement: "SymbioImpact demonstrates measurable global sustainability impact" },
    { year: "2030", achievement: "Symbiotic intelligence becomes the dominant paradigm" }
  ];

  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main>
        {/* Hero Section */}
        <section className="pt-32 pb-20 px-6">
          <div className="container mx-auto text-center">
            <Typography
              as="h1"
              variant="4xl"
              weight="bold"
              gradient="consciousness"
              className="mb-6"
            >
              {t('pages.vision.title')}
            </Typography>
            <Typography
              variant="xl"
              color="secondary"
              className="max-w-3xl mx-auto mb-8"
            >
              {t('pages.vision.subtitle')}
            </Typography>
            <Typography
              variant="lg"
              color="consciousness"
              className="max-w-2xl mx-auto mb-12"
            >
              {t('pages.vision.description')}
            </Typography>

            {/* Vision Evolution */}
            <div className="max-w-4xl mx-auto">
              <Card variant="quantum" className="p-8 rounded-[32px] border-consciousness/25">
                <div className="text-center mb-6">
                  <Typography variant="xl" weight="semibold" color="consciousness" className="mb-4">
                    The Evolution of Symbiotic Intelligence
                  </Typography>
                  <Typography variant="base" color="secondary">
                    Watch the evolution of intelligence unfold through bio-inspired patterns
                  </Typography>
                </div>

                <div className="h-80 rounded-[20px] overflow-hidden bg-gradient-to-br from-creativity-500/10 via-creativity-400/5 to-creativity-secondary/10 border border-creativity-500/20">
                  <div className="w-full h-full flex items-center justify-center">
                    <div className="text-center space-y-4">
                      <Eye className="w-16 h-16 mx-auto text-creativity-400 animate-pulse" />
                      <Typography variant="lg" color="creativity" className="opacity-70">
                        Vision Evolution Visualization
                      </Typography>
                      <Typography variant="sm" color="secondary" className="opacity-50">
                        Bio-inspired intelligence patterns
                      </Typography>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </section>

        {/* Vision Pillars */}
        <section className="py-20 bg-gradient-to-b from-surface-void/50 to-surface-void">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <Typography 
                as="h2" 
                variant="3xl" 
                weight="bold" 
                color="consciousness"
                className="mb-4"
              >
                The Three Pillars of Our Vision
              </Typography>
              <Typography 
                variant="lg" 
                color="secondary"
                className="max-w-2xl mx-auto"
              >
                The foundational principles that will guide the transformation to symbiotic intelligence
              </Typography>
            </div>

            <div className="grid lg:grid-cols-3 gap-8">
              {visionPillars.map((pillar, index) => {
                const Icon = pillar.icon;
                return (
                  <Card key={index} variant="quantum" className="p-8 text-center">
                    <div className="w-20 h-20 rounded-cellular bg-consciousness/20 flex items-center justify-center mx-auto mb-6">
                      <Icon className="w-10 h-10 text-consciousness" />
                    </div>
                    
                    <Typography 
                      as="h3" 
                      variant="xl" 
                      weight="semibold" 
                      color="consciousness"
                      className="mb-4"
                    >
                      {pillar.title}
                    </Typography>
                    
                    <Typography 
                      variant="sm" 
                      color="secondary"
                      className="mb-6 leading-relaxed"
                    >
                      {pillar.description}
                    </Typography>

                    <div className="flex items-center justify-center space-x-2">
                      <div className="w-2 h-2 rounded-full bg-creativity"></div>
                      <Typography 
                        variant="xs" 
                        color="creativity"
                        weight="medium"
                      >
                        Timeline: {pillar.timeframe}
                      </Typography>
                    </div>
                  </Card>
                );
              })}
            </div>
          </div>
        </section>

        {/* Future Scenarios */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <Typography 
                as="h2" 
                variant="3xl" 
                weight="bold" 
                gradient="neural"
                className="mb-4"
              >
                Future Scenarios
              </Typography>
              <Typography 
                variant="lg" 
                color="secondary"
                className="max-w-2xl mx-auto"
              >
                Real-world applications of symbiotic intelligence that will transform society
              </Typography>
            </div>

            <div className="space-y-8">
              {futureScenarios.map((scenario, index) => (
                <Card key={index} variant="neural" className="p-8">
                  <div className="grid lg:grid-cols-3 gap-6 items-center">
                    <div className="lg:col-span-2">
                      <Typography 
                        as="h3" 
                        variant="xl" 
                        weight="semibold" 
                        color="consciousness"
                        className="mb-3"
                      >
                        {scenario.title}
                      </Typography>
                      <Typography 
                        variant="sm" 
                        color="secondary"
                        className="leading-relaxed"
                      >
                        {scenario.description}
                      </Typography>
                    </div>
                    <div className="text-center lg:text-right">
                      <div className="inline-flex items-center space-x-2 px-4 py-2 bg-consciousness/10 border border-consciousness/30 rounded-cellular">
                        <Target className="w-4 h-4 text-consciousness" />
                        <Typography 
                          variant="sm" 
                          color="consciousness"
                          weight="medium"
                        >
                          {scenario.impact}
                        </Typography>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Roadmap */}
        <section className="py-20 bg-gradient-to-b from-surface-void/50 to-surface-void">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <Typography 
                as="h2" 
                variant="3xl" 
                weight="bold" 
                color="consciousness"
                className="mb-4"
              >
                Our Roadmap to the Future
              </Typography>
              <Typography 
                variant="lg" 
                color="secondary"
                className="max-w-2xl mx-auto"
              >
                Key milestones on the journey to symbiotic intelligence
              </Typography>
            </div>

            <div className="space-y-6">
              {milestones.map((milestone, index) => (
                <Card key={index} variant="neural" className="p-6">
                  <div className="flex items-center space-x-6">
                    <div className="w-20 h-20 rounded-cellular bg-gradient-to-br from-consciousness/20 to-creativity/10 flex items-center justify-center flex-shrink-0">
                      <Typography 
                        variant="lg" 
                        weight="bold" 
                        color="consciousness"
                      >
                        {milestone.year}
                      </Typography>
                    </div>
                    <Typography 
                      variant="lg" 
                      color="secondary"
                      className="flex-1"
                    >
                      {milestone.achievement}
                    </Typography>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="py-20">
          <div className="container mx-auto px-6 text-center">
            <Typography
              as="h2"
              variant="3xl"
              weight="bold"
              gradient="consciousness"
              className="mb-6"
            >
              {t('pages.vision.cta.title')}
            </Typography>
            <Typography
              variant="lg"
              color="secondary"
              className="max-w-2xl mx-auto mb-8"
            >
              {t('pages.vision.cta.description')}
            </Typography>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="quantum"
                size="lg"
                rightIcon={<ArrowRight className="w-5 h-5" />}
              >
                {t('pages.vision.cta.buttons.primary')}
              </Button>
              <Button
                variant="secondary"
                size="lg"
              >
                {t('pages.vision.cta.buttons.secondary')}
              </Button>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default Vision;
